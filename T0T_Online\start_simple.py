#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版T0交易系统启动脚本
用于测试和调试Smart API集成问题
"""

import logging
import sys
from datetime import datetime, timedelta
from config_loader import config

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger = logging.getLogger(__name__)

try:
    import smart
    logger.info("Smart API 导入成功")
except ImportError as e:
    logger.error(f"无法导入Smart API: {e}")
    sys.exit(1)

class TradingDayCalculator:
    """交易日计算工具"""
    
    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（简化版本，排除周末）"""
        return date.weekday() < 5
    
    @staticmethod
    def get_previous_trading_days(count: int, from_date: datetime = None) -> list:
        """获取前N个交易日"""
        if from_date is None:
            from_date = datetime.now()
        
        trading_days = []
        current_date = from_date - timedelta(days=1)
        
        while len(trading_days) < count:
            if TradingDayCalculator.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date -= timedelta(days=1)
        
        return trading_days

class SimpleT0System:
    """简化版T0交易系统"""
    
    def __init__(self):
        self.config = config
        self.target_symbols = config.get_target_symbols()
        self.is_running = False
        logger.info("简化版T0系统初始化完成")
    
    def test_smart_api(self):
        """测试Smart API基本功能"""
        try:
            logger.info("开始测试Smart API...")
            
            # 测试通知功能
            try:
                smart.notice("🧪 T0系统测试通知")
                logger.info("✅ 通知功能测试成功")
            except Exception as e:
                logger.error(f"❌ 通知功能测试失败: {e}")
            
            # 测试数据查询功能
            try:
                # 获取前两个交易日
                trading_days = TradingDayCalculator.get_previous_trading_days(2)
                if trading_days:
                    start_date = trading_days[-1]
                    end_date = trading_days[0]
                    logger.info(f"前两个交易日: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                
                # 测试数据查询（使用第一个标的）
                if self.target_symbols:
                    symbol = self.target_symbols[0]
                    logger.info(f"测试查询 {symbol} 的数据...")
                    
                    # 尝试不同的API调用方式
                    try:
                        data = smart.query_data(
                            code=symbol,
                            start=start_date.strftime('%Y%m%d'),
                            end=end_date.strftime('%Y%m%d'),
                            period='1m'
                        )
                        logger.info(f"✅ query_data 调用成功，返回数据类型: {type(data)}")
                        if hasattr(data, '__len__'):
                            logger.info(f"数据长度: {len(data)}")
                    except Exception as e:
                        logger.error(f"❌ query_data 调用失败: {e}")
                        
                        # 尝试其他API方法
                        try:
                            data = smart.query_market_data(
                                symbol=symbol,
                                period='1m'
                            )
                            logger.info(f"✅ query_market_data 调用成功")
                        except Exception as e2:
                            logger.error(f"❌ query_market_data 也失败: {e2}")
                
            except Exception as e:
                logger.error(f"❌ 数据查询测试失败: {e}")
            
            # 测试订阅功能
            try:
                if self.target_symbols:
                    smart.subscribe(self.target_symbols)
                    logger.info(f"✅ 订阅功能测试成功: {self.target_symbols}")
            except Exception as e:
                logger.error(f"❌ 订阅功能测试失败: {e}")
            
            # 测试定时器功能
            try:
                def test_timer_callback():
                    logger.info("⏰ 定时器回调测试")
                
                smart.add_time_interval(60, test_timer_callback)
                logger.info("✅ 定时器设置成功")
            except Exception as e:
                logger.error(f"❌ 定时器设置失败: {e}")
                
        except Exception as e:
            logger.error(f"Smart API测试过程中发生错误: {e}")
    
    def test_trading_time(self):
        """测试交易时间检查"""
        try:
            now = datetime.now()
            current_time = now.strftime('%H:%M')
            
            # 简单的交易时间检查
            is_morning = '09:30' <= current_time <= '11:30'
            is_afternoon = '13:00' <= current_time <= '15:00'
            is_trading_time = is_morning or is_afternoon
            
            logger.info(f"当前时间: {current_time}")
            logger.info(f"是否为交易时间: {is_trading_time}")
            
            if is_trading_time:
                logger.info("✅ 当前为交易时间")
            else:
                logger.info("⏰ 当前为非交易时间")
                
        except Exception as e:
            logger.error(f"交易时间检查失败: {e}")
    
    def start(self):
        """启动简化版系统"""
        try:
            logger.info("🚀 启动简化版T0交易系统...")
            
            # 发送启动通知
            try:
                smart.notice("🚀 简化版T0系统启动中...")
            except Exception as e:
                logger.warning(f"启动通知发送失败: {e}")
            
            # 运行各项测试
            self.test_smart_api()
            self.test_trading_time()
            
            self.is_running = True
            
            # 发送成功通知
            try:
                smart.notice("✅ 简化版T0系统启动完成！")
            except Exception as e:
                logger.warning(f"成功通知发送失败: {e}")
            
            logger.info("✅ 简化版T0交易系统启动成功")
            
        except Exception as e:
            logger.error(f"❌ 简化版系统启动失败: {e}")
            try:
                smart.notice(f"❌ 简化版T0系统启动失败: {e}")
            except:
                pass
    
    def stop(self):
        """停止系统"""
        self.is_running = False
        logger.info("简化版T0交易系统已停止")

def main():
    """主函数"""
    try:
        # 创建并启动简化版系统
        system = SimpleT0System()
        system.start()
        
        # 保持运行状态
        logger.info("系统运行中，按 Ctrl+C 停止...")
        
        import time
        while system.is_running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("收到停止信号")
        if 'system' in locals():
            system.stop()
    except Exception as e:
        logger.error(f"系统运行时发生错误: {e}")

if __name__ == "__main__":
    main()
