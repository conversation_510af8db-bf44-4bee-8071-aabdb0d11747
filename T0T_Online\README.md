# T0T_Online - 基于Smart平台的T0交易策略系统

## 项目概述

T0T_Online是从T0T本地交易策略系统迁移而来的在线交易系统，基于Smart交易平台的Python API实现。该系统实现了完整的T0交易策略，包括实时数据获取、技术指标计算、信号检测、自动交易执行和风险管理。

## 核心功能

### 1. 实时数据管理
- **MarketDataManager**: 实时行情数据订阅和管理
- 支持多股票同时监控
- 自动生成分钟级K线数据
- 数据回调机制支持策略实时响应

### 2. 技术指标计算
- **TechnicalIndicators**: 完整的技术指标计算库
- MACD指标：快线、慢线、信号线、柱状图
- KDJ指标：K值、D值、J值
- 支持自定义参数配置

### 3. T0交易策略
- **T0TradingStrategy**: 核心交易策略实现
- 基于技术指标的买卖信号检测
- 相对位置分析（日内高低点）
- 防重复交易机制
- 多重信号确认

### 4. 交易执行
- **TradeExecutor**: 自动交易执行器
- Smart API集成下单
- 订单状态跟踪
- 成交记录管理

### 5. 系统控制
- **T0TradingSystem**: 主控制器
- 事件驱动架构
- 实时监控和日志
- 优雅启停机制

## 文件结构

```
T0T_Online/
├── start.py              # 主程序入口，Smart平台集成
├── config.yaml           # 系统配置文件
├── config_loader.py      # 配置加载器
├── test_system.py        # 系统测试脚本
└── README.md             # 项目文档
```

## 配置说明

### 交易配置 (trading)
- `target_symbols`: 交易标的列表
- `min_trade_portion`: 最小交易比例 (1/8)
- `max_trade_portion`: 最大交易比例 (1/3)
- `min_trade_interval`: 最小交易间隔(分钟)
- `max_consecutive_trades`: 最大连续同方向交易次数

### 技术指标配置 (indicators)
- `macd`: MACD参数 (快线12, 慢线26, 信号线9)
- `kdj`: KDJ参数 (周期9)

### 风险管理配置 (risk_management)
- `max_position_ratio`: 最大仓位比例 (80%)
- `stop_loss_ratio`: 止损比例 (5%)
- `daily_max_trades`: 每日最大交易次数 (20)

### 信号检测配置 (signal_detection)
- `buy_signals`: 买入信号参数
- `sell_signals`: 卖出信号参数

## 交易策略逻辑

### 买入信号检测
1. **位置条件**: 价格在日内相对低位（< 60%）
2. **MACD信号**: MACD底背离（DIF上升但价格下跌）
3. **KDJ信号**: KDJ超卖反弹（K < 20且上升）
4. **价格信号**: 接近近期低点且有反弹迹象

### 卖出信号检测
1. **位置条件**: 价格在日内相对高位（> 40%）
2. **MACD信号**: MACD顶背离（DIF下降但价格上涨）
3. **KDJ信号**: KDJ超买回调（K > 80且下降）
4. **价格信号**: 接近近期高点且有回调迹象

### 防重复交易机制
- 最小交易间隔控制
- 连续同方向交易限制
- 最小价格变动要求
- 每日交易次数限制

## 安装和运行

### 1. 环境准备
```bash
# 安装依赖
pip install pandas pyyaml

# 确保Smart API环境已配置
```

### 2. 配置设置
编辑 `config.yaml` 文件，设置：
- 交易标的
- 风险参数
- 技术指标参数

### 3. 系统测试
```bash
python test_system.py
```

### 4. 启动系统
在Smart平台中加载 `start.py` 文件，系统将自动：
- 订阅配置的股票行情
- 开始实时策略分析
- 自动执行T0交易

## 系统架构

```
Smart平台
    ↓
start.py (主入口)
    ↓
T0TradingSystem (主控制器)
    ├── MarketDataManager (数据管理)
    ├── T0TradingStrategy (策略分析)
    └── TradeExecutor (交易执行)
```

## 关键特性

### 1. 事件驱动
- 基于Smart API的事件回调机制
- 实时响应行情变化
- 异步处理提高性能

### 2. 配置化
- YAML配置文件
- 热加载配置支持
- 灵活的参数调整

### 3. 风险控制
- 多层次风险管理
- 实时监控和报警
- 自动止损机制

### 4. 可扩展性
- 模块化设计
- 易于添加新指标
- 支持多策略组合

## 注意事项

1. **实盘风险**: 本系统为实盘交易系统，请在充分测试后使用
2. **参数调优**: 建议根据不同市场环境调整策略参数
3. **监控重要**: 运行时请密切监控系统状态和交易结果
4. **合规要求**: 请确保交易行为符合相关法规要求

## 技术支持

如有问题，请检查：
1. Smart API连接状态
2. 配置文件格式
3. 日志输出信息
4. 系统测试结果

## 版本历史

- v1.0: 初始版本，完成T0T策略迁移
- 基于原T0T系统的完整功能实现
- 集成Smart API的实时交易能力
