2025-06-30 22:35:12,907 - DEBUG - 27908 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - <module> - 23 - Smart API 导入成功
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - __init__ - 59 - 简化版T0系统初始化完成
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - start - 160 - 🚀 启动简化版T0交易系统...
2025-06-30 22:35:12,976 - WARNING - 27908 - start_simple.py - start - 166 - 启动通知发送失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 64 - 开始测试Smart API...
2025-06-30 22:35:12,977 - ERROR - 27908 - start_simple.py - test_smart_api - 71 - ❌ 通知功能测试失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 80 - 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 85 - 测试查询 600036.SH 的数据...
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 99 - ❌ query_data 调用失败: module 'smart' has no attribute 'query_data'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 109 - ❌ query_market_data 也失败: module 'smart' has no attribute 'query_market_data'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 120 - ❌ 订阅功能测试失败: module 'smart' has no attribute 'subscribe'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 130 - ❌ 定时器设置失败: module 'smart' has no attribute 'add_time_interval'
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 146 - 当前时间: 22:35
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 147 - 是否为交易时间: False
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 152 - ⏰ 当前为非交易时间
2025-06-30 22:35:12,978 - WARNING - 27908 - start_simple.py - start - 178 - 成功通知发送失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - start - 180 - ✅ 简化版T0交易系统启动成功
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - main - 202 - 系统运行中，按 Ctrl+C 停止...
2025-06-30 22:35:32,437 - INFO - 27908 - start_simple.py - main - 209 - 收到停止信号
2025-06-30 22:35:32,438 - INFO - 27908 - start_simple.py - stop - 192 - 简化版T0交易系统已停止
2025-06-30 22:35:58,130 - DEBUG - 36188 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:35:58,154 - INFO - 36188 - check_smart_api.py - <module> - 14 - Smart API 导入成功
2025-06-30 22:36:32,001 - DEBUG - 39176 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:36:32,027 - INFO - 39176 - check_smart_instance.py - <module> - 14 - Smart API 导入成功
2025-06-30 22:37:58,503 - DEBUG - 29416 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:37:59,585 - INFO - 29416 - config_loader.py - load_config - 31 - 成功加载配置文件: config.yaml
2025-06-30 22:38:49,538 - DEBUG - 39160 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:38:49,560 - INFO - 39160 - test_fixed_api.py - <module> - 21 - Smart API 导入成功
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 52 - 🧪 开始测试修复后的Smart API...
2025-06-30 22:38:49,561 - ERROR - 39160 - test_fixed_api.py - test_fixed_smart_api - 59 - ❌ 通知功能测试失败: 'str' object has no attribute 'get'
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 67 - ✅ 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 78 - 测试查询 600036.SH 的数据...
2025-06-30 22:38:49,562 - DEBUG - 39160 - core.py - query_data_async - 2578 - query_data_async start
2025-06-30 22:38:49,562 - DEBUG - 39160 - core.py - query_data_async - 2605 - query_data_async send
2025-06-30 22:40:41,580 - DEBUG - 37428 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:40:41,602 - INFO - 37428 - start_fixed.py - <module> - 25 - Smart API 导入成功
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - __init__ - 210 - 修复版T0交易系统初始化完成
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - start - 224 - 🚀 启动修复版T0交易系统...
2025-06-30 22:40:41,603 - WARNING - 37428 - start_fixed.py - _send_notice_safe - 219 - 通知发送失败（不影响系统运行）: 'str' object has no attribute 'get'
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - start - 230 - 开始加载warm-up数据...
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - load_warmup_data - 130 - 开始加载 600036.SH 的warm-up数据...
2025-06-30 22:40:41,604 - INFO - 37428 - start_fixed.py - load_warmup_data - 136 - 获取 600036.SH 从 2025-06-26 到 2025-06-27 的数据
2025-06-30 22:40:41,604 - DEBUG - 37428 - core.py - query_data_async - 2578 - query_data_async start
2025-06-30 22:40:41,604 - DEBUG - 37428 - core.py - query_data_async - 2605 - query_data_async send
2025-06-30 23:11:21,995 - DEBUG - 40748 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:11:22,016 - INFO - 40748 - test_final_fixes.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:11:22,017 - INFO - 40748 - test_final_fixes.py - main - 153 - 🚀 开始最终修复功能测试...
2025-06-30 23:11:22,017 - INFO - 40748 - test_final_fixes.py - test_notice_function - 49 - 🧪 测试通知功能...
2025-06-30 23:12:05,913 - DEBUG - 39968 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:12:05,933 - INFO - 39968 - check_trading_day_api.py - <module> - 20 - Smart API 导入成功
2025-06-30 23:12:05,935 - INFO - 39968 - check_trading_day_api.py - main - 110 - 🚀 开始检查交易日API...
2025-06-30 23:12:05,935 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 27 - 🔍 检查Smart API中的交易日相关方法...
2025-06-30 23:12:05,936 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 41 - 找到可能的交易日相关方法: ['get_trading_day', 'get_trading_day_async', 'query_bar_today', 'query_bar_today_async', 'query_market_data', 'query_market_data_async', 'trade_share_mem']
2025-06-30 23:12:05,936 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ get_trading_day: <class 'method'> - 可调用
2025-06-30 23:12:05,936 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ get_trading_day_async: <class 'method'> - 可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ query_bar_today: <class 'method'> - 可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ query_bar_today_async: <class 'method'> - 可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ query_market_data: <class 'method'> - 可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 48 - ✅ query_market_data_async: <class 'method'> - 可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 53 - ❌ trade_share_mem: <class 'bytearray'> - 不可调用
2025-06-30 23:12:05,937 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 58 - 
📋 Smart API 所有可用方法 (116 个):
2025-06-30 23:12:05,938 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  1. Event
2025-06-30 23:12:05,938 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  2. Type
2025-06-30 23:12:05,938 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  3. accountManager
2025-06-30 23:12:05,938 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  4. account_map
2025-06-30 23:12:05,938 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  5. addEventListener
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  6. add_self_select_stock
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  7. add_time_interval
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  8. add_timer
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 -  9. bar_share_mem
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 10. book
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 11. bookManager
2025-06-30 23:12:05,939 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 12. bt_static_quote_size
2025-06-30 23:12:05,941 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 13. bt_time_map
2025-06-30 23:12:05,942 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 14. cache
2025-06-30 23:12:05,943 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 15. callJSFunction
2025-06-30 23:12:05,943 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 16. cancel_order
2025-06-30 23:12:05,943 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 17. clear_time_interval
2025-06-30 23:12:05,943 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 18. clear_timer
2025-06-30 23:12:05,943 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 19. close
2025-06-30 23:12:05,944 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 20. closeCallback
2025-06-30 23:12:05,944 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 21. createStrategy
2025-06-30 23:12:05,944 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 22. credit_cash_repay
2025-06-30 23:12:05,944 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 23. current_account
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 24. current_date
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 25. current_time
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 26. dataType
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 27. del_self_select_stock
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 28. emit
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 29. etf_map
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 30. etf_profit_map
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 31. getBondReverseRepoList
2025-06-30 23:12:05,945 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 32. getConvertableBond
2025-06-30 23:12:05,946 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 33. getETFBasket
2025-06-30 23:12:05,946 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 34. getETFList
2025-06-30 23:12:05,946 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 35. getIPOList
2025-06-30 23:12:05,946 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 36. getInstrument
2025-06-30 23:12:05,946 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 37. getSystemSet
2025-06-30 23:12:05,947 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 38. get_quoteExIndicatorMap
2025-06-30 23:12:05,947 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 39. get_quoteIndicatorMap
2025-06-30 23:12:05,947 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 40. get_quoteMap
2025-06-30 23:12:05,947 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 41. get_subExIndicatorMap
2025-06-30 23:12:05,948 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 42. get_subIndicatorMap
2025-06-30 23:12:05,948 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 43. get_submap
2025-06-30 23:12:05,948 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 44. get_trading_day
2025-06-30 23:12:05,948 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 45. get_trading_day_async
2025-06-30 23:12:05,949 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 46. hideCallback
2025-06-30 23:12:05,949 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 47. index_map
2025-06-30 23:12:05,949 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 48. init
2025-06-30 23:12:05,949 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 49. initCallback
2025-06-30 23:12:05,949 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 50. initEvent
2025-06-30 23:12:05,950 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 51. init_end
2025-06-30 23:12:05,950 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 52. insertAlgoOrder
2025-06-30 23:12:05,950 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 53. insert_order
2025-06-30 23:12:05,951 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 54. instrument_list
2025-06-30 23:12:05,951 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 55. instrument_map
2025-06-30 23:12:05,951 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 56. instrument_map_by_type
2025-06-30 23:12:05,951 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 57. ipo_map
2025-06-30 23:12:05,952 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 58. isDebugReady
2025-06-30 23:12:05,952 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 59. isReady
2025-06-30 23:12:05,952 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 60. is_back_test
2025-06-30 23:12:05,953 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 61. listeners
2025-06-30 23:12:05,953 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 62. msgRemind
2025-06-30 23:12:05,953 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 63. notice
2025-06-30 23:12:05,953 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 64. on
2025-06-30 23:12:05,953 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 65. on_bar
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 66. on_close
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 67. on_hide
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 68. on_indicator
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 69. on_init
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 70. on_reset
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 71. on_show
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 72. order_share_mem
2025-06-30 23:12:05,954 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 73. pluginName
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 74. queryCreditAssets
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 75. querySelfSelectStockList
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 76. query_bar
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 77. query_bar_async
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 78. query_bar_today
2025-06-30 23:12:05,955 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 79. query_bar_today_async
2025-06-30 23:12:05,956 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 80. query_data
2025-06-30 23:12:05,956 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 81. query_data_async
2025-06-30 23:12:05,956 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 82. query_data_page
2025-06-30 23:12:05,956 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 83. query_data_page_async
2025-06-30 23:12:05,956 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 84. query_etf_purchase_redemption_top_limit
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 85. query_etf_ssquote_sh
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 86. query_etf_ssquote_sh_async
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 87. query_market_data
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 88. query_market_data_async
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 89. query_source_quote_list
2025-06-30 23:12:05,957 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 90. quote_share_mem
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 91. registCallableFunction
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 92. removeAllListeners
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 93. removeEventListener
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 94. resetCallback
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 95. reverse_repo_list
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 96. showCallback
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 97. startStrategy
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 98. static_quote_share_mem
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 99. strategyManager
2025-06-30 23:12:05,958 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 100. strategy_map
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 101. submit_source_apply
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 102. subscribe
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 103. subscribeETFProfit
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 104. subscribe_bar
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 105. subscribe_bar_ex
2025-06-30 23:12:05,960 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 106. subscribe_index
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 107. subscribe_indicator
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 108. systemset
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 109. trade_share_mem
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 110. unsubscribe
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 111. unsubscribeETFProfit
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 112. unsubscribe_bar
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 113. unsubscribe_bar_ex
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 114. unsubscribe_index
2025-06-30 23:12:05,961 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 115. unsubscribe_indicator
2025-06-30 23:12:05,962 - INFO - 39968 - check_trading_day_api.py - check_trading_day_methods - 60 - 116. utils
2025-06-30 23:12:05,962 - INFO - 39968 - check_trading_day_api.py - test_simple_trading_day_check - 66 - 
🧪 测试简单的交易日检查...
2025-06-30 23:12:05,962 - INFO - 39968 - check_trading_day_api.py - test_simple_trading_day_check - 94 - 找到方法: get_trading_day
2025-06-30 23:12:05,962 - WARNING - 39968 - check_trading_day_api.py - test_simple_trading_day_check - 100 - ⚠️ get_trading_day(20250628) 调用失败: get_trading_day() takes 1 positional argument but 2 were given
2025-06-30 23:12:05,963 - INFO - 39968 - check_trading_day_api.py - test_simple_trading_day_check - 102 - ❌ 未找到可用的交易日查询方法
2025-06-30 23:12:05,963 - INFO - 39968 - check_trading_day_api.py - main - 118 - ✅ 检查完成
2025-06-30 23:12:43,516 - DEBUG - 37468 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:12:43,534 - INFO - 37468 - test_trading_day_method.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:12:43,535 - INFO - 37468 - test_trading_day_method.py - main - 139 - 🚀 开始测试交易日方法...
2025-06-30 23:12:43,535 - INFO - 37468 - test_trading_day_method.py - test_get_trading_day - 28 - 🧪 测试get_trading_day方法...
2025-06-30 23:12:43,535 - DEBUG - 37468 - core.py - get_trading_day - 2895 - get_trading_day send
2025-06-30 23:13:26,745 - DEBUG - 41052 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:13:26,765 - INFO - 41052 - start_fixed.py - <module> - 25 - Smart API 导入成功
2025-06-30 23:13:26,765 - INFO - 41052 - start_fixed.py - __init__ - 238 - 修复版T0交易系统初始化完成
2025-06-30 23:13:26,767 - INFO - 41052 - start_fixed.py - start - 259 - 🚀 启动修复版T0交易系统...
2025-06-30 23:14:13,543 - DEBUG - 34044 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:14:13,561 - INFO - 34044 - test_notice_and_trading_day.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:14:13,562 - INFO - 34044 - test_notice_and_trading_day.py - main - 205 - 🚀 开始测试修复后的功能...
2025-06-30 23:14:13,562 - INFO - 34044 - test_notice_and_trading_day.py - test_notice_with_dict - 79 - 🧪 测试字典格式通知功能...
2025-06-30 23:14:40,604 - DEBUG - 40556 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:14:40,624 - INFO - 40556 - test_simple_notice.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:14:40,624 - INFO - 40556 - test_simple_notice.py - main - 99 - 🚀 开始简单通知测试...
2025-06-30 23:14:40,624 - INFO - 40556 - test_simple_notice.py - test_simple_notice - 28 - 🧪 测试简单通知...
2025-06-30 23:14:40,625 - INFO - 40556 - test_simple_notice.py - test_simple_notice - 40 - 准备发送通知: {'level': 'info', 'title': '测试', 'msg': '简单测试', 'duration': 3000, 'timestamp': '2025-06-30 23:14:40'}
2025-06-30 23:15:26,590 - DEBUG - 41728 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:15:26,610 - INFO - 41728 - start_fixed.py - <module> - 25 - Smart API 导入成功
2025-06-30 23:15:26,611 - INFO - 41728 - start_fixed.py - __init__ - 238 - 修复版T0交易系统初始化完成
2025-06-30 23:15:26,611 - INFO - 41728 - start_fixed.py - start - 266 - 🚀 启动修复版T0交易系统...
2025-06-30 23:15:26,611 - INFO - 41728 - start_fixed.py - _send_notice_safe - 244 - 准备发送通知: 🚀 修复版T0交易系统启动中... (级别: info)
2025-06-30 23:15:26,612 - INFO - 41728 - start_fixed.py - _send_notice_safe - 248 - 📢 [INFO] 🚀 修复版T0交易系统启动中...
2025-06-30 23:15:26,612 - INFO - 41728 - start_fixed.py - start - 272 - 开始加载warm-up数据...
2025-06-30 23:15:26,612 - INFO - 41728 - start_fixed.py - load_warmup_data - 158 - 开始加载 600036.SH 的warm-up数据...
2025-06-30 23:15:26,612 - DEBUG - 41728 - core.py - query_bar_today - 2842 - query_bar_today send
2025-06-30 23:16:17,000 - DEBUG - 40220 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:16:17,019 - INFO - 40220 - test_final_system.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:16:17,020 - INFO - 40220 - test_final_system.py - main - 202 - 🚀 开始最终系统测试...
2025-06-30 23:16:17,020 - INFO - 40220 - test_final_system.py - safe_notice - 79 - 📢 [INFO] 开始最终系统测试
2025-06-30 23:16:17,021 - INFO - 40220 - test_final_system.py - test_trading_day_calculation - 86 - 🧪 测试交易日计算...
2025-06-30 23:16:17,021 - DEBUG - 40220 - core.py - query_bar_today - 2842 - query_bar_today send
2025-06-30 23:18:29,166 - DEBUG - 38644 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:18:29,186 - INFO - 38644 - test_simple_fixes.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:18:29,186 - INFO - 38644 - test_simple_fixes.py - main - 183 - 🚀 开始简单修复功能测试...
2025-06-30 23:18:29,186 - INFO - 38644 - test_simple_fixes.py - safe_notice - 84 - 📢 [INFO] 开始简单修复功能测试
2025-06-30 23:18:29,188 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 91 - 🧪 测试交易日计算...
2025-06-30 23:18:29,188 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 95 - 最近7天的交易日判断:
2025-06-30 23:18:29,188 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-30 (周一): ✅ 交易日
2025-06-30 23:18:29,188 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-29 (周日): ❌ 非交易日
2025-06-30 23:18:29,189 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-28 (周六): ❌ 非交易日
2025-06-30 23:18:29,189 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-27 (周五): ✅ 交易日
2025-06-30 23:18:29,189 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-26 (周四): ✅ 交易日
2025-06-30 23:18:29,189 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-25 (周三): ✅ 交易日
2025-06-30 23:18:29,189 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-24 (周二): ✅ 交易日
2025-06-30 23:18:29,190 - INFO - 38644 - test_simple_fixes.py - test_trading_day_calculation - 111 - ✅ 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 23:18:29,190 - INFO - 38644 - test_simple_fixes.py - safe_notice - 84 - 📢 [SUCCESS] 交易日计算成功: 2025-06-26 到 2025-06-27
2025-06-30 23:18:29,190 - INFO - 38644 - test_simple_fixes.py - test_basic_smart_api - 126 - 🧪 测试基本Smart API功能...
2025-06-30 23:18:29,190 - INFO - 38644 - test_simple_fixes.py - test_basic_smart_api - 134 - 测试订阅功能: ['600036.SH', '600000.SH']
2025-06-30 23:18:29,191 - ERROR - 38644 - core.py - subscribe - 1063 - unhashable type: 'list'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 969, in subscribe
    account = self.account_map[account_id]
TypeError: unhashable type: 'list'
Stack (most recent call last):
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 223, in <module>
    main()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 193, in main
    api_results = test_basic_smart_api()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 135, in test_basic_smart_api
    smart.smart.subscribe(symbols)
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 1063, in subscribe
    logger.error(e,exc_info=True, stack_info=True)
2025-06-30 23:18:29,192 - INFO - 38644 - test_simple_fixes.py - test_basic_smart_api - 136 - ✅ 订阅功能测试成功
2025-06-30 23:18:29,192 - INFO - 38644 - test_simple_fixes.py - safe_notice - 84 - 📢 [SUCCESS] 订阅功能测试成功
2025-06-30 23:18:29,192 - INFO - 38644 - test_simple_fixes.py - test_basic_smart_api - 153 - 测试定时器功能...
2025-06-30 23:19:59,962 - DEBUG - 35308 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:19:59,982 - INFO - 35308 - test_simple_fixes.py - <module> - 21 - Smart API 导入成功
2025-06-30 23:19:59,982 - INFO - 35308 - test_simple_fixes.py - main - 186 - 🚀 开始简单修复功能测试...
2025-06-30 23:19:59,982 - INFO - 35308 - test_simple_fixes.py - safe_notice - 84 - 📢 [INFO] 开始简单修复功能测试
2025-06-30 23:19:59,983 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 91 - 🧪 测试交易日计算...
2025-06-30 23:19:59,983 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 95 - 最近7天的交易日判断:
2025-06-30 23:19:59,983 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-30 (周一): ✅ 交易日
2025-06-30 23:19:59,983 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-29 (周日): ❌ 非交易日
2025-06-30 23:19:59,984 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-28 (周六): ❌ 非交易日
2025-06-30 23:19:59,984 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-27 (周五): ✅ 交易日
2025-06-30 23:19:59,984 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-26 (周四): ✅ 交易日
2025-06-30 23:19:59,984 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-25 (周三): ✅ 交易日
2025-06-30 23:19:59,985 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 102 -   2025-06-24 (周二): ✅ 交易日
2025-06-30 23:19:59,985 - INFO - 35308 - test_simple_fixes.py - test_trading_day_calculation - 111 - ✅ 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 23:19:59,985 - INFO - 35308 - test_simple_fixes.py - safe_notice - 84 - 📢 [SUCCESS] 交易日计算成功: 2025-06-26 到 2025-06-27
2025-06-30 23:19:59,985 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 126 - 🧪 测试基本Smart API功能...
2025-06-30 23:19:59,985 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 134 - 测试订阅功能: ['600036.SH', '600000.SH']
2025-06-30 23:19:59,986 - ERROR - 35308 - core.py - subscribe - 1063 - '600036.SH'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 969, in subscribe
    account = self.account_map[account_id]
KeyError: '600036.SH'
Stack (most recent call last):
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 226, in <module>
    main()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 196, in main
    api_results = test_basic_smart_api()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 137, in test_basic_smart_api
    smart.smart.subscribe(symbol)
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 1063, in subscribe
    logger.error(e,exc_info=True, stack_info=True)
2025-06-30 23:19:59,986 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 138 - 已订阅 600036.SH
2025-06-30 23:19:59,986 - ERROR - 35308 - core.py - subscribe - 1063 - '600000.SH'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 969, in subscribe
    account = self.account_map[account_id]
KeyError: '600000.SH'
Stack (most recent call last):
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 226, in <module>
    main()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 196, in main
    api_results = test_basic_smart_api()
  File "C:\Users\<USER>\.xtp-smart\plugins\T0T_Online\test_simple_fixes.py", line 137, in test_basic_smart_api
    smart.smart.subscribe(symbol)
  File "C:\Users\<USER>\AppData\Local\Programs\xtp_rich_client\bin\cpython\lib\site-packages\smart.egg\smart\core.py", line 1063, in subscribe
    logger.error(e,exc_info=True, stack_info=True)
2025-06-30 23:19:59,987 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 138 - 已订阅 600000.SH
2025-06-30 23:19:59,987 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 139 - ✅ 订阅功能测试成功
2025-06-30 23:19:59,987 - INFO - 35308 - test_simple_fixes.py - safe_notice - 84 - 📢 [SUCCESS] 订阅功能测试成功
2025-06-30 23:19:59,988 - INFO - 35308 - test_simple_fixes.py - test_basic_smart_api - 156 - 测试定时器功能...
2025-06-30 23:20:11,330 - DEBUG - 10424 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 23:20:11,348 - INFO - 10424 - start_fixed.py - <module> - 25 - Smart API 导入成功
2025-06-30 23:20:11,348 - INFO - 10424 - start_fixed.py - __init__ - 244 - 修复版T0交易系统初始化完成
2025-06-30 23:20:11,348 - INFO - 10424 - start_fixed.py - start - 272 - 🚀 启动修复版T0交易系统...
2025-06-30 23:20:11,349 - INFO - 10424 - start_fixed.py - _send_notice_safe - 250 - 准备发送通知: 🚀 修复版T0交易系统启动中... (级别: info)
2025-06-30 23:20:11,349 - INFO - 10424 - start_fixed.py - _send_notice_safe - 254 - 📢 [INFO] 🚀 修复版T0交易系统启动中...
2025-06-30 23:20:11,350 - INFO - 10424 - start_fixed.py - start - 278 - 开始加载warm-up数据...
2025-06-30 23:20:11,350 - INFO - 10424 - start_fixed.py - load_warmup_data - 161 - 开始加载 600036.SH 的warm-up数据...
2025-06-30 23:20:11,350 - INFO - 10424 - start_fixed.py - load_warmup_data - 167 - 获取 600036.SH 从 2025-06-26 到 2025-06-27 的数据
2025-06-30 23:20:11,350 - DEBUG - 10424 - core.py - query_data_async - 2578 - query_data_async start
2025-06-30 23:20:11,350 - DEBUG - 10424 - core.py - query_data_async - 2605 - query_data_async send
2025-07-01 22:57:10,719 - DEBUG - 6432 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 22:58:49,514 - DEBUG - 20748 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 22:59:06,854 - DEBUG - 6836 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 22:59:07,136 - INFO - 6836 - config_loader.py - load_config - 31 - 成功加载配置文件: config.yaml
2025-07-01 22:59:37,004 - DEBUG - 24132 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:00:08,774 - DEBUG - 11568 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:00:18,969 - DEBUG - 15208 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:00:19,249 - INFO - 15208 - config_loader.py - load_config - 31 - 成功加载配置文件: config.yaml
2025-07-01 23:00:27,144 - DEBUG - 12056 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:02:17,375 - DEBUG - 7648 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:02:17,388 - INFO - 7648 - start.py - _send_notice - 923 - 准备发送通知: 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整 (级别: info)
2025-07-01 23:03:05,161 - DEBUG - 11988 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:03:05,174 - INFO - 11988 - start.py - _send_notice - 923 - 准备发送通知: 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整 (级别: info)
2025-07-01 23:03:45,535 - DEBUG - 19448 - logging_config.py - <module> - 75 - initial logging: py.log
2025-07-01 23:03:45,547 - INFO - 19448 - start.py - _send_notice - 923 - 准备发送通知: 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整 (级别: info)
2025-07-01 23:03:45,589 - INFO - 19448 - start.py - _send_notice - 949 - 测试模式：跳过通知发送: 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整
2025-07-01 23:03:45,589 - INFO - 19448 - start.py - update_position - 904 - 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整
2025-07-02 22:55:02,559 - DEBUG - 26716 - logging_config.py - <module> - 75 - initial logging: py.log
