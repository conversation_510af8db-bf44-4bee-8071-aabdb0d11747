"""
T0T_Online - 基于Smart平台的T0交易策略系统
主要功能：
1. 实时行情数据获取
2. 技术指标计算
3. T0交易信号检测
4. 自动交易执行
5. 风险管理
"""

from smart import *
import logging
import pandas as pd
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Callable
from config_loader import config

class TradingDayCalculator:
    """交易日计算工具"""

    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（通过查询数据判断）"""
        try:
            # 检查是否为周末
            if date.weekday() >= 5:  # 周六、周日
                return False

            # 回测模式下，无法使用query_data，使用简化逻辑
            if smart.is_back_test:
                logger.debug(f"回测模式：假设 {date.strftime('%Y-%m-%d')} 为交易日（非周末）")
                return True

            # 实盘模式下，通过查询数据来判断是否为交易日
            # 使用一个常见的股票代码进行测试查询
            test_symbol = "600036.SH"  # 招商银行，流动性好的股票

            # 设置查询时间范围（当天的交易时间）
            start_time = date.strftime('%Y-%m-%d 09:30:00')
            end_time = date.strftime('%Y-%m-%d 15:00:00')

            # 创建一个标志来接收查询结果
            query_result = {'has_data': False, 'completed': True}

            # 发起同步查询
            datalist = smart.query_data(
                method="bar",
                inParams={
                    "code": test_symbol,
                    "start_date": start_time,
                    "end_date": end_time,
                    "period": "60m",
                },
            )

            # 如果有数据返回，说明是交易日
            query_result['has_data'] = datalist is not None and len(datalist) > 0
            logger.debug(f"交易日查询结果: {len(datalist) if datalist else 0} 条数据")

            if query_result['completed']:
                return query_result['has_data']
            else:
                logger.warning("查询交易日数据超时，使用备用逻辑")
                return date.weekday() < 5

        except Exception as e:
            logger.warning(f"查询交易日信息失败: {e}")
            # 备用逻辑：排除周末
            return date.weekday() < 5

    @staticmethod
    def get_previous_trading_days(count: int, from_date: datetime = None) -> List[datetime]:
        """获取前N个交易日"""
        if from_date is None:
            from_date = datetime.now()
            if smart.is_back_test:
                from_date = str(smart.current_time)
                dt = datetime.strptime(from_date[:14], '%Y%m%d%H%M%S')
                milliseconds = len(from_date[14:]) > 0 and int(from_date[14:]) or 0
                from_date = dt.replace(microsecond=milliseconds * 1000)

        trading_days = []
        current_date = from_date - timedelta(days=1)  # 从前一天开始

        while len(trading_days) < count:
            if TradingDayCalculator.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date -= timedelta(days=1)

        return trading_days

    @staticmethod
    def get_trading_day_range(days_back: int, from_date: datetime = None) -> tuple:
        """获取交易日范围（开始日期，结束日期）"""
        if from_date is None:
            from_date = datetime.now()
            if smart.is_back_test:
                from_date = str(smart.current_time)
                dt = datetime.strptime(from_date[:14], '%Y%m%d%H%M%S')
                milliseconds = len(from_date[14:]) > 0 and int(from_date[14:]) or 0
                from_date = dt.replace(microsecond=milliseconds * 1000)

        trading_days = TradingDayCalculator.get_previous_trading_days(days_back, from_date)
        if trading_days:
            start_date = trading_days[-1]  # 最早的交易日
            end_date = trading_days[0]     # 最近的交易日
            return start_date, end_date
        else:
            # 如果没有找到交易日，返回默认范围
            end_date = from_date - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back)
            return start_date, end_date

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger = logging.getLogger(__name__)

class T0TradingConfig:
    """T0交易配置"""
    def __init__(self):
        # 从配置文件加载参数
        trading_config = config.get_trading_config()
        indicators_config = config.get_indicators_config()
        risk_config = config.get_risk_management_config()
        signal_config = config.get_signal_detection_config()

        # 交易标的配置
        self.target_symbols = config.get_target_symbols()

        # T0交易参数
        self.min_trade_portion = trading_config.get('min_trade_portion', 1/8)
        self.max_trade_portion = trading_config.get('max_trade_portion', 1/3)
        self.price_threshold = trading_config.get('price_threshold', 0.01)
        self.transaction_cost_rate = trading_config.get('transaction_cost_rate', 0.0014)

        # 防重复交易参数
        self.min_trade_interval = trading_config.get('min_trade_interval', 3)
        self.max_consecutive_trades = trading_config.get('max_consecutive_trades', 2)
        self.min_price_change = trading_config.get('min_price_change', 0.002)

        # 技术指标参数
        macd_config = indicators_config.get('macd', {})
        self.macd_fast = macd_config.get('fast_period', 12)
        self.macd_slow = macd_config.get('slow_period', 26)
        self.macd_signal = macd_config.get('signal_period', 9)

        kdj_config = indicators_config.get('kdj', {})
        self.kdj_period = kdj_config.get('period', 9)

        # 风险管理参数
        self.max_position_ratio = risk_config.get('max_position_ratio', 0.8)
        self.stop_loss_ratio = risk_config.get('stop_loss_ratio', 0.05)
        self.daily_max_trades = risk_config.get('daily_max_trades', 20)

        # 信号检测参数
        self.buy_signals = signal_config.get('buy_signals', {})
        self.sell_signals = signal_config.get('sell_signals', {})

        # 数据管理参数
        self.warmup_required_periods = trading_config.get('warmup_required_periods', 240)  # warm-up需要的数据周期数

        # 存储原始配置以支持get方法
        self._config_data = {
            'trading': trading_config,
            'indicators': indicators_config,
            'risk_management': risk_config,
            'signal_detection': signal_config
        }

    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        try:
            keys = key_path.split('.')
            current = self._config_data

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default

            return current
        except Exception:
            return default

class TechnicalIndicators:
    """技术指标计算类"""

    @staticmethod
    def calculate_macd(data: pd.DataFrame, fast=12, slow=26, signal=9) -> pd.DataFrame:
        """计算MACD指标"""
        df = data.copy()

        # 计算EMA
        df['ema_fast'] = df['close'].ewm(span=fast, adjust=False).mean()
        df['ema_slow'] = df['close'].ewm(span=slow, adjust=False).mean()

        # 计算MACD
        df['macd_dif'] = df['ema_fast'] - df['ema_slow']
        df['macd_dea'] = df['macd_dif'].ewm(span=signal, adjust=False).mean()
        df['macd_bar'] = 2 * (df['macd_dif'] - df['macd_dea'])

        # 删除中间计算列
        df = df.drop(['ema_fast', 'ema_slow'], axis=1)

        return df

    @staticmethod
    def calculate_kdj(data: pd.DataFrame, period=9) -> pd.DataFrame:
        """计算KDJ指标"""
        df = data.copy()

        # 计算RSV
        low_min = df['low'].rolling(window=period).min()
        high_max = df['high'].rolling(window=period).max()
        df['rsv'] = (df['close'] - low_min) / (high_max - low_min) * 100

        # 计算KDJ
        df['kdj_k'] = df['rsv'].ewm(alpha=1/3, adjust=False).mean()
        df['kdj_d'] = df['kdj_k'].ewm(alpha=1/3, adjust=False).mean()
        df['kdj_j'] = 3 * df['kdj_k'] - 2 * df['kdj_d']

        return df

class MarketDataManager:
    """市场数据管理器"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.quote_data = {}  # 存储最新行情数据
        self.minute_data = {}  # 存储分钟级数据
        self.current_minute_quotes = {}  # 存储当前分钟的所有tick数据
        self.previous_minute_quotes = {}  # 存储上一分钟的tick数据（乒乓缓存）
        self.data_callbacks = []  # 数据回调函数列表
        self.is_warmed_up = {}  # 记录各股票是否已完成warm-up
        self.warmup_required_periods = config.warmup_required_periods  # 从配置获取warm-up需要的数据周期数

    def subscribe_quotes(self, symbols: List[str]):
        """订阅行情数据"""
        def subscribe_callback(quoteList, err):
            if err:
                logger.debug("get error from subscribe:%s",err)
            else:
                for i in range(len(quoteList)):
                    logger.debug("subscribe quote:%s", smart.utils.toString(quoteList[i]))
        try:
            smart.subscribe(codes=symbols, callback=subscribe_callback)
            logger.info(f"所有标的订阅完成: {symbols}")
        except Exception as e:
            logger.error(f"订阅行情数据失败: {e}")

    def load_warmup_data(self, symbols: List[str]):
        """加载warm-up数据（前两个交易日的分钟数据）"""
        try:
            # 回测模式检测与跳过
            if smart.is_back_test:
                logger.info("回测模式：跳过历史数据查询，将通过实时数据流完成warm-up")
                logger.info("策略将在前2个交易日收集数据用于warm-up，第3个交易日开始允许交易")

                # 初始化回测模式的交易日跟踪
                self.backtest_trading_day_count = 0
                self.backtest_current_date = None

                # 将所有目标股票的is_warmed_up初始化为False
                for symbol in symbols:
                    self.is_warmed_up[symbol] = False
                    # 初始化分钟数据存储
                    if symbol not in self.minute_data:
                        self.minute_data[symbol] = deque(maxlen=1000)
                    logger.info(f"{symbol} 初始化为未warm-up状态，将通过实时数据收集")

                logger.info("回测模式warm-up初始化完成")
                return

            # 实盘模式：使用原有的历史数据查询逻辑
            logger.info("实盘模式：开始查询历史数据进行warm-up")
            for symbol in symbols:
                logger.info(f"开始加载 {symbol} 的warm-up数据...")

                # 使用交易日计算工具获取前两个交易日
                try:
                    # 获取前两个交易日的日期范围
                    start_date, end_date = TradingDayCalculator.get_trading_day_range(2)

                    logger.info(f"获取 {symbol} 从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的数据")

                    # 使用Smart API获取历史数据
                    # 创建查询结果容器
                    query_result = {'data': None, 'completed': False, 'error': None}

                    # 发起同步查询
                    datalist = smart.query_data(
                        method="market_data",
                        inParams={
                            "code": symbol,
                            "start_date": start_date.strftime('%Y-%m-%d 09:30:00'),
                            "end_date": end_date.strftime('%Y-%m-%d 15:00:00'),
                        },
                    )

                    if len(datalist) > 0:
                        query_result['completed'] = True
                        query_result['data'] = pd.DataFrame(datalist)
                        logger.info(f"获取到 {symbol} 的 {len(datalist) if datalist else 0} 条历史数据")
                    else:
                        logger.info(f"查询失败")

                    if query_result['completed']:
                        # 初始化分钟数据存储
                        if symbol not in self.minute_data:
                            self.minute_data[symbol] = deque(maxlen=1000)

                        # ohlcv处理 - 添加数据验证和异常处理
                        data = query_result["data"]
                        logger.debug(f"{symbol} 原始数据样本: {data.head(2).to_dict() if len(data) > 0 else 'Empty'}")

                        # 验证必要字段存在
                        required_fields = ['date_time', 'last_price', 'volume']
                        missing_fields = [field for field in required_fields if field not in data.columns]
                        if missing_fields:
                            raise ValueError(f"缺少必要字段: {missing_fields}")

                        # 安全的时间戳解析
                        def safe_parse_timestamp(date_time_str):
                            try:
                                if pd.isna(date_time_str) or str(date_time_str).strip() == '':
                                    return None
                                date_time_str = str(date_time_str).strip()

                                # 基础时间部分（前14位）
                                if len(date_time_str) < 14:
                                    logger.warning(f"时间戳格式异常: {date_time_str}")
                                    return None

                                base_time = pd.to_datetime(date_time_str[:14], format='%Y%m%d%H%M%S')

                                # 毫秒部分（第14位之后）
                                if len(date_time_str) > 14:
                                    ms_part = date_time_str[14:].strip()
                                    if ms_part and ms_part.isdigit():
                                        milliseconds = int(ms_part)
                                        return base_time + pd.Timedelta(milliseconds=milliseconds)

                                return base_time
                            except Exception as e:
                                logger.warning(f"解析时间戳失败 {date_time_str}: {e}")
                                return None

                        # 应用时间戳解析
                        data['timestamp'] = data['date_time'].apply(safe_parse_timestamp)

                        # 过滤掉无效时间戳的数据
                        valid_data = data.dropna(subset=['timestamp'])
                        if len(valid_data) == 0:
                            raise ValueError("所有时间戳都无效")

                        if len(valid_data) < len(data):
                            logger.warning(f"{symbol} 过滤掉 {len(data) - len(valid_data)} 条无效时间戳数据")

                        data = valid_data.set_index('timestamp')

                        # 使用更安全的聚合方式
                        try:
                            ohlc = data.resample('1min').agg({
                                'last_price': ['first', 'max', 'min', 'last']
                            })
                            ohlc.columns = ['open', 'high', 'low', 'close']

                            # 处理成交量
                            minute_volume = data['volume'].resample('1min').last().diff().fillna(data['volume'].resample('1min').last())
                            ohlc['volume'] = minute_volume
                            ohlc = ohlc.dropna(how='all')

                        except Exception as resample_error:
                            logger.error(f"{symbol} 数据重采样失败: {resample_error}")
                            raise

                        # 将历史数据转换为标准格式并存储
                        ohlc = ohlc.reset_index().to_dict('records')
                        self.minute_data[symbol].extend(ohlc)

                        # 检查是否有足够的数据进行warm-up
                        if len(self.minute_data[symbol]) >= self.warmup_required_periods:
                            self.is_warmed_up[symbol] = True
                            logger.info(f"{symbol} warm-up完成，加载了 {len(self.minute_data[symbol])} 条历史数据")
                        else:
                            logger.warning(f"{symbol} warm-up数据不足，仅加载了 {len(self.minute_data[symbol])} 条数据")
                    else:
                        if query_result['completed']:
                            logger.warning(f"无法获取 {symbol} 的历史数据: {query_result['error']}")
                        else:
                            logger.warning(f"获取 {symbol} 历史数据超时")

                except Exception as api_error:
                    logger.error(f"获取 {symbol} 历史数据失败: {api_error}")
                    # 如果无法获取历史数据，标记为需要实时积累
                    self.is_warmed_up[symbol] = False

        except Exception as e:
            logger.error(f"加载warm-up数据失败: {e}")

    def is_symbol_ready_for_trading(self, symbol: str) -> bool:
        """检查股票是否准备好进行交易（已完成warm-up）"""
        return self.is_warmed_up.get(symbol, False)

    def on_quote_received(self, quote):
        """行情数据回调 - 处理实时tick数据"""
        try:
            # 检查是否在交易时间内，非交易时间直接跳过
            if not self._is_trading_time(quote.data_time):
                return

            symbol = quote.code
            # 更新最新行情
            self.quote_data[symbol] = {
                'symbol': symbol,
                'last_price': quote.last_price,
                'volume': quote.volume,
                'turnover': quote.turnover,
                'bid_price': quote.bid_price[0] if quote.bid_price else 0,
                'ask_price': quote.ask_price[0] if quote.ask_price else 0,
                'timestamp': quote.data_time
            }

            # 记录tick数据并处理分钟K线
            minute_completed = self._update_minute_data(symbol, quote)

            # 如果新的一分钟完成，触发交易策略分析
            if minute_completed and self.is_warmed_up.get(symbol, False):
                logger.debug(f"{symbol} 新的一分钟完成，触发策略分析")
                self._trigger_strategy_analysis(symbol)
                logger.debug(f"{symbol} 策略分析完成，继续等待下一分钟数据")

            # 通知所有回调函数（用于其他监控功能）
            for callback in self.data_callbacks:
                callback(symbol, self.quote_data[symbol])

            # 确保系统持续运行，每次处理完tick数据后立即返回等待下一个tick

        except Exception as e:
            logger.error(f"处理行情数据失败: {e}")

    def _update_minute_data(self, symbol: str, quote) -> bool:
        """更新分钟级数据，返回是否完成了新的一分钟K线"""
        # 使用quote的时间戳（如果有）或当前时间
        if hasattr(quote, 'data_time') and quote.data_time:
            current_time = quote.data_time
        else:
            current_time = datetime.now()
            if smart.is_back_test:
                current_time = str(smart.current_time)
        if type(current_time) == str:
            dt = datetime.strptime(current_time[:14], '%Y%m%d%H%M%S')
            milliseconds = len(current_time[14:]) > 0 and int(current_time[14:]) or 0
            current_time = dt.replace(microsecond=milliseconds * 1000)
        minute_key = current_time.replace(second=0, microsecond=0)
        minute_completed = False

        # 初始化数据结构（只在第一次访问时初始化，避免重置已有数据）
        if symbol not in self.minute_data:
            self.minute_data[symbol] = deque(maxlen=1000)  # 保留最近1000分钟数据
        if symbol not in self.current_minute_quotes:
            self.current_minute_quotes[symbol] = []
        if symbol not in self.previous_minute_quotes:
            self.previous_minute_quotes[symbol] = []

        # 回测模式：交易日跟踪和warm-up管理
        if smart.is_back_test:
            current_date = current_time.date()

            # 检查是否进入新的交易日
            if not hasattr(self, 'backtest_current_date') or self.backtest_current_date != current_date:
                if hasattr(self, 'backtest_current_date') and self.backtest_current_date is not None:
                    # 新交易日开始
                    self.backtest_trading_day_count += 1
                    logger.info(f"回测模式：进入第 {self.backtest_trading_day_count} 个交易日 ({current_date})")
                else:
                    # 第一个交易日
                    self.backtest_trading_day_count = 1
                    logger.info(f"回测模式：开始第 1 个交易日 ({current_date})")

                self.backtest_current_date = current_date

        # 检查是否进入新的分钟
        current_minute_key = minute_key

        # 获取当前正在收集的分钟（如果有数据的话）
        current_collecting_minute = None
        if self.current_minute_quotes[symbol]:
            # 从第一个tick获取当前收集的分钟
            first_tick = self.current_minute_quotes[symbol][0]
            current_collecting_minute = first_tick['timestamp'].replace(second=0, microsecond=0)

        # 判断是否进入新分钟：当前tick的分钟与正在收集的分钟不同
        if current_collecting_minute is None:
            # 第一次收集数据，直接开始
            minute_completed = False
        elif current_minute_key > current_collecting_minute:
            # 新的分钟开始，处理上一分钟的数据
            logger.debug(f"{symbol} 检测到新分钟: {current_collecting_minute} -> {current_minute_key}")

            # 使用乒乓缓存保存上一分钟数据
            self.previous_minute_quotes[symbol] = self.current_minute_quotes[symbol].copy()

            # 从上一分钟的tick数据生成OHLCV
            minute_bar = self._create_minute_bar_from_quotes(
                self.current_minute_quotes[symbol],
                current_collecting_minute
            )

            if minute_bar:
                self.minute_data[symbol].append(minute_bar)
                minute_completed = True
                logger.debug(f"{symbol} 完成分钟K线: O={minute_bar['open']:.3f} H={minute_bar['high']:.3f} L={minute_bar['low']:.3f} C={minute_bar['close']:.3f} V={minute_bar['volume']:.0f}")

            # 清空当前分钟数据，开始收集新分钟
            self.current_minute_quotes[symbol] = []

            # 检查是否达到warm-up要求
            if not self.is_warmed_up.get(symbol, False):
                data_sufficient = len(self.minute_data[symbol]) >= self.warmup_required_periods

                if smart.is_back_test:
                    # 回测模式：需要满足两个条件
                    # 1. 数据量足够 2. 已经是第3个交易日或以后
                    trading_day_ready = hasattr(self, 'backtest_trading_day_count') and self.backtest_trading_day_count >= 3

                    if data_sufficient and trading_day_ready:
                        self.is_warmed_up[symbol] = True
                        logger.info(f"{symbol} 回测模式warm-up完成: 第{self.backtest_trading_day_count}个交易日, {len(self.minute_data[symbol])}条分钟数据")
                    elif data_sufficient and not trading_day_ready:
                        logger.debug(f"{symbol} 数据已足够({len(self.minute_data[symbol])}条)，但仍在warm-up期(第{getattr(self, 'backtest_trading_day_count', 0)}个交易日)")
                else:
                    # 实盘模式：只需要数据量足够
                    if data_sufficient:
                        self.is_warmed_up[symbol] = True
                        logger.info(f"{symbol} 实盘模式通过实时数据积累完成warm-up")
        else:
            # 同一分钟内，继续收集数据
            minute_completed = False

        # 将当前tick数据添加到当前分钟的收集中（无论是否新分钟都要收集）
        quote_data = {
            'timestamp': current_time,
            'last_price': quote.last_price,
            'volume': getattr(quote, 'volume', 0),
            'turnover': getattr(quote, 'turnover', 0),
            'bid_price': getattr(quote, 'bid_price', [quote.last_price])[0] if hasattr(quote, 'bid_price') else quote.last_price,
            'ask_price': getattr(quote, 'ask_price', [quote.last_price])[0] if hasattr(quote, 'ask_price') else quote.last_price
        }
        self.current_minute_quotes[symbol].append(quote_data)

        # 在每次tick后检查warm-up状态（特别是回测模式下的交易日变化）
        if not self.is_warmed_up.get(symbol, False):
            data_sufficient = len(self.minute_data[symbol]) >= self.warmup_required_periods

            if smart.is_back_test:
                # 回测模式：需要满足两个条件
                # 1. 数据量足够 2. 至少完成第1个交易日（不需要等到第3天）
                trading_day_ready = hasattr(self, 'backtest_trading_day_count') and self.backtest_trading_day_count >= 1

                if data_sufficient and trading_day_ready:
                    self.is_warmed_up[symbol] = True
                    logger.info(f"{symbol} 回测模式warm-up完成: 第{self.backtest_trading_day_count}个交易日, {len(self.minute_data[symbol])}条分钟数据")
                elif data_sufficient and not trading_day_ready:
                    logger.debug(f"{symbol} 数据已足够({len(self.minute_data[symbol])}条)，等待第1个交易日完成")
                elif not data_sufficient and trading_day_ready:
                    logger.debug(f"{symbol} 第{self.backtest_trading_day_count}个交易日已开始，但数据不足({len(self.minute_data[symbol])}/{self.warmup_required_periods}条)")
            else:
                # 实盘模式：只需要数据量足够
                if data_sufficient:
                    self.is_warmed_up[symbol] = True
                    logger.info(f"{symbol} 实盘模式通过实时数据积累完成warm-up")

        # 记录数据收集状态
        logger.debug(f"{symbol} 收集tick: 价格={quote.last_price:.3f}, 当前分钟tick数={len(self.current_minute_quotes[symbol])}, 新分钟={'是' if minute_completed else '否'}")

        return minute_completed

    def _create_minute_bar_from_quotes(self, quotes_list: list, minute_timestamp: datetime) -> dict:
        """从tick数据列表创建分钟K线，参考load_warmup_data中的OHLCV处理"""
        if not quotes_list:
            return None

        try:
            # 转换为DataFrame进行处理，类似load_warmup_data的逻辑
            df = pd.DataFrame(quotes_list)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')

            # 按照load_warmup_data的方式生成OHLCV
            # 使用正确的pandas聚合方法
            prices = df['last_price']
            ohlc_data = {
                'first': prices.iloc[0],
                'max': prices.max(),
                'min': prices.min(),
                'last': prices.iloc[-1]
            }

            # 计算成交量变化，类似load_warmup_data的逻辑
            # minute_volume = data['volume'].resample('1min').last().diff().fillna(data['volume'].resample('1min').last())
            if len(df) > 1:
                volume_change = df['volume'].iloc[-1] - df['volume'].iloc[0]
                volume = max(0, volume_change)  # 确保成交量为正
            else:
                volume = df['volume'].iloc[0] if len(df) > 0 else 0

            minute_bar = {
                'timestamp': minute_timestamp,
                'open': float(ohlc_data['first']),
                'high': float(ohlc_data['max']),
                'low': float(ohlc_data['min']),
                'close': float(ohlc_data['last']),
                'volume': float(volume)
            }

            return minute_bar

        except Exception as e:
            logger.error(f"创建分钟K线失败: {e}")
            # 降级处理：使用简单的OHLCV计算
            prices = [q['last_price'] for q in quotes_list]
            volumes = [q.get('volume', 0) for q in quotes_list]

            return {
                'timestamp': minute_timestamp,
                'open': prices[0],
                'high': max(prices),
                'low': min(prices),
                'close': prices[-1],
                'volume': max(volumes) - min(volumes) if len(volumes) > 1 else volumes[0] if volumes else 0
            }

    def _trigger_strategy_analysis(self, symbol: str):
        """触发交易策略分析 - 处理完成后继续等待下一分钟数据"""
        try:
            # 获取足够的分钟数据进行分析
            if len(self.minute_data[symbol]) < 30:
                logger.debug(f"{symbol} 数据不足，需要{30}条，当前{len(self.minute_data[symbol])}条")
                return

            # 转换为DataFrame进行技术分析
            df_data = pd.DataFrame(list(self.minute_data[symbol]))
            logger.debug(f"{symbol} 触发策略分析，数据条数: {len(df_data)}")

            # 直接调用策略分析，不依赖回调机制
            # 这样可以确保分析完成后立即返回，继续处理下一分钟数据
            if hasattr(self, 'strategy') and hasattr(self, 'executor'):
                self._analyze_and_trade(symbol, df_data)
            else:
                logger.warning(f"{symbol} 策略或执行器未初始化，跳过分析")

        except Exception as e:
            logger.error(f"触发策略分析失败 {symbol}: {e}")
            # 即使出错也要继续处理，不能中断数据流

    def _analyze_and_trade(self, symbol: str, data: pd.DataFrame):
        """分析数据并执行交易 - 快速处理后立即返回，确保持续数据流"""
        try:
            # 参数验证
            if symbol is None or symbol.strip() == '':
                logger.error("_analyze_and_trade: symbol参数为空")
                return

            if data is None:
                logger.error(f"_analyze_and_trade: {symbol} data参数为None")
                return

            if not isinstance(data, pd.DataFrame):
                logger.error(f"_analyze_and_trade: {symbol} data不是DataFrame类型: {type(data)}")
                return

            if len(data) == 0:
                logger.warning(f"_analyze_and_trade: {symbol} data为空DataFrame")
                return

            # 验证数据格式
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"_analyze_and_trade: {symbol} 缺少必要列: {missing_columns}")
                return

            logger.debug(f"{symbol} 开始策略分析，数据行数: {len(data)}")

            # 验证策略对象
            if not hasattr(self, 'strategy') or self.strategy is None:
                logger.error(f"_analyze_and_trade: {symbol} strategy对象未初始化")
                return

            # 执行交易策略分析
            signal = self.strategy.analyze_market_data(symbol, data)

            # 验证信号格式
            if signal is None:
                logger.debug(f"{symbol} 策略返回None信号")
                return

            if not isinstance(signal, dict):
                logger.error(f"_analyze_and_trade: {symbol} 策略返回的信号不是字典类型: {type(signal)}")
                return

            # 验证信号必要字段
            required_signal_fields = ['action', 'strength', 'reason']
            missing_signal_fields = [field for field in required_signal_fields if field not in signal]
            if missing_signal_fields:
                logger.error(f"_analyze_and_trade: {symbol} 信号缺少必要字段: {missing_signal_fields}")
                return

            logger.debug(f"{symbol} 策略分析完成，信号: {signal}")

            # 如果有交易信号，执行交易
            if signal['action'] in ['buy', 'sell']:
                logger.info(f"{symbol} 生成交易信号: {signal['action']} 强度:{signal['strength']:.2f} 原因:{signal['reason']}")

                # 验证执行器
                if not hasattr(self, 'executor') or self.executor is None:
                    logger.error(f"_analyze_and_trade: {symbol} executor对象未初始化")
                    return

                # 异步执行交易，不阻塞数据处理
                success = self.executor.execute_trade(
                    symbol=symbol,
                    action=signal['action'],
                    price=signal.get('price', data.iloc[-1]['close']),
                    strength=signal['strength'],
                    reason=signal['reason']
                )

                if success:
                    logger.info(f"{symbol} 交易执行成功")
                else:
                    logger.warning(f"{symbol} 交易执行失败")
            else:
                logger.debug(f"{symbol} 无交易信号({signal['action']})，继续监控")

        except Exception as e:
            logger.error(f"分析交易时发生错误 {symbol}: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 出错也要继续，保证数据流不中断

    def _is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        now = datetime.now()
        if smart.is_back_test:
            now = str(smart.current_time)
            dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
            milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
            now = dt.replace(microsecond=milliseconds * 1000)
        current_time = now.strftime('%H:%M')

        trading_hours = config.get_trading_hours()
        morning_start = trading_hours.get('morning_start', '09:30')
        morning_end = trading_hours.get('morning_end', '11:30')
        afternoon_start = trading_hours.get('afternoon_start', '13:00')
        afternoon_end = trading_hours.get('afternoon_end', '15:00')

        # 检查是否在交易时间段内
        in_morning = morning_start <= current_time <= morning_end
        in_afternoon = afternoon_start <= current_time <= afternoon_end

        return in_morning or in_afternoon

    def _is_trading_time(self, time: str) -> bool:
        """检查是否在交易时间内"""
        dt = datetime.strptime(time[:14], '%Y%m%d%H%M%S')
        milliseconds = len(time[14:]) > 0 and int(time[14:]) or 0
        time = dt.replace(microsecond=milliseconds * 1000)
        current_time = time.strftime('%H:%M')

        trading_hours = config.get_trading_hours()
        morning_start = trading_hours.get('morning_start', '09:30')
        morning_end = trading_hours.get('morning_end', '11:30')
        afternoon_start = trading_hours.get('afternoon_start', '13:00')
        afternoon_end = trading_hours.get('afternoon_end', '15:00')

        # 检查是否在交易时间段内
        in_morning = morning_start <= current_time <= morning_end
        in_afternoon = afternoon_start <= current_time <= afternoon_end

        return in_morning or in_afternoon

    def get_minute_data(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """获取分钟级数据"""
        if symbol not in self.minute_data or len(self.minute_data[symbol]) == 0:
            return pd.DataFrame()

        # 转换为DataFrame
        data = list(self.minute_data[symbol])[-periods:]
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)

        return df

    def add_data_callback(self, callback: Callable):
        """添加数据回调函数"""
        self.data_callbacks.append(callback)

class T0TradingStrategy:
    """T0交易策略核心类"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.positions = {}  # 持仓信息
        self.trade_state = {}  # 交易状态
        self.daily_trades = 0  # 当日交易次数
        self.total_profit = 0.0  # 累计收益

        # 初始化交易状态
        for symbol in config.target_symbols:
            self.trade_state[symbol] = {
                'last_buy_price': None,
                'last_sell_price': None,
                'last_buy_time': None,
                'last_sell_time': None,
                'daily_high': None,
                'daily_low': None,
                'consecutive_same_direction': 0,
                'last_trade_direction': None
            }

    def analyze_market_data(self, symbol: str, minute_data: pd.DataFrame) -> Dict:
        """分析市场数据，生成交易信号"""
        try:
            logger.debug(f"{symbol} 开始策略分析，数据长度: {len(minute_data)}")

            if len(minute_data) < 30:  # 数据不足
                logger.debug(f"{symbol} 数据不足，需要30条，当前{len(minute_data)}条")
                return {'action': 'hold', 'strength': 0.0, 'reason': '数据不足'}

            # 计算技术指标
            logger.debug(f"{symbol} 开始计算技术指标...")
            try:
                data_with_indicators = TechnicalIndicators.calculate_macd(minute_data,
                                                                         self.config.macd_fast,
                                                                         self.config.macd_slow,
                                                                         self.config.macd_signal)
                data_with_indicators = TechnicalIndicators.calculate_kdj(data_with_indicators,
                                                                       self.config.kdj_period)
                logger.debug(f"{symbol} 技术指标计算完成")
            except Exception as indicator_error:
                logger.error(f"{symbol} 技术指标计算失败: {indicator_error}")
                return {'action': 'hold', 'strength': 0.0, 'reason': '技术指标计算失败'}

            # 获取最新数据
            latest = data_with_indicators.iloc[-1]
            prev = data_with_indicators.iloc[-2] if len(data_with_indicators) > 1 else latest

            # 记录关键技术指标值（安全格式化）
            def safe_format(value, decimals=4):
                if value is None or value == 'N/A':
                    return 'N/A'
                try:
                    return f"{float(value):.{decimals}f}"
                except (ValueError, TypeError):
                    return str(value)

            logger.debug(f"{symbol} 技术指标 - "
                        f"MACD: {safe_format(latest.get('macd'))}, "
                        f"MACD信号: {safe_format(latest.get('macd_signal'))}, "
                        f"KDJ_K: {safe_format(latest.get('kdj_k'), 2)}, "
                        f"KDJ_D: {safe_format(latest.get('kdj_d'), 2)}, "
                        f"价格: {latest['close']:.3f}")

            # 更新日内高低点
            current_high = minute_data['high'].max()
            current_low = minute_data['low'].min()

            if self.trade_state[symbol]['daily_high'] is None:
                self.trade_state[symbol]['daily_high'] = current_high
                self.trade_state[symbol]['daily_low'] = current_low
                logger.debug(f"{symbol} 初始化日内高低点: 高{current_high:.3f}, 低{current_low:.3f}")
            else:
                old_high = self.trade_state[symbol]['daily_high']
                old_low = self.trade_state[symbol]['daily_low']
                self.trade_state[symbol]['daily_high'] = max(self.trade_state[symbol]['daily_high'], current_high)
                self.trade_state[symbol]['daily_low'] = min(self.trade_state[symbol]['daily_low'], current_low)

                if self.trade_state[symbol]['daily_high'] != old_high or self.trade_state[symbol]['daily_low'] != old_low:
                    logger.debug(f"{symbol} 更新日内高低点: 高{self.trade_state[symbol]['daily_high']:.3f}, 低{self.trade_state[symbol]['daily_low']:.3f}")

            # 计算相对位置
            daily_range = self.trade_state[symbol]['daily_high'] - self.trade_state[symbol]['daily_low']
            if daily_range > 0:
                relative_position = (latest['close'] - self.trade_state[symbol]['daily_low']) / daily_range
            else:
                relative_position = 0.5

            logger.debug(f"{symbol} 相对位置: {relative_position:.2f} (日内区间: {daily_range:.3f})")

            # 检测T0交易信号
            signal = self._detect_t0_signals(symbol, latest, prev, relative_position, data_with_indicators)

            logger.debug(f"{symbol} 策略分析完成，信号: {signal}")
            return signal

        except Exception as e:
            logger.error(f"{symbol} 策略分析异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {'action': 'hold', 'strength': 0.0, 'reason': f'策略分析异常: {str(e)}'}

    def _detect_t0_signals(self, symbol: str, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测T0交易信号"""
        logger.debug(f"{symbol} 开始信号检测，相对位置: {relative_position:.2f}")

        # 检查是否应该跳过交易（防重复交易）
        if self._should_skip_trade(symbol, latest['close']):
            logger.debug(f"{symbol} 跳过交易 - 防重复交易")
            return {'action': 'hold', 'strength': 0.0, 'reason': '防重复交易'}

        # T0买入信号检测
        logger.debug(f"{symbol} 检测买入信号...")
        buy_signal = self._detect_buy_signal(latest, prev, relative_position, data)
        logger.debug(f"{symbol} 买入信号结果: {buy_signal}")

        if buy_signal['should_buy']:
            logger.info(f"{symbol} 检测到买入信号: {buy_signal['reason']}, 强度: {buy_signal['strength']:.2f}")
            return {
                'action': 'buy',
                'strength': buy_signal['strength'],
                'reason': buy_signal['reason'],
                'price': latest['close']
            }

        # T0卖出信号检测
        logger.debug(f"{symbol} 检测卖出信号...")
        sell_signal = self._detect_sell_signal(latest, prev, relative_position, data)
        logger.debug(f"{symbol} 卖出信号结果: {sell_signal}")

        if sell_signal['should_sell']:
            logger.info(f"{symbol} 检测到卖出信号: {sell_signal['reason']}, 强度: {sell_signal['strength']:.2f}")
            return {
                'action': 'sell',
                'strength': sell_signal['strength'],
                'reason': sell_signal['reason'],
                'price': latest['close']
            }

        logger.debug(f"{symbol} 无明确交易信号")
        return {'action': 'hold', 'strength': 0.0, 'reason': '无明确信号'}

    def _detect_buy_signal(self, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测买入信号"""
        should_buy = False
        strength = 1.0
        reasons = []

        # 从配置获取买入信号参数
        buy_config = self.config.buy_signals
        max_relative_pos = buy_config.get('max_relative_position', 0.6)
        macd_weight = buy_config.get('macd_divergence_weight', 0.5)
        kdj_threshold = buy_config.get('kdj_oversold_threshold', 20)
        kdj_weight = buy_config.get('kdj_rebound_weight', 0.3)
        low_threshold = buy_config.get('near_low_threshold', 1.002)
        low_weight = buy_config.get('near_low_weight', 0.4)

        # 条件1：价格在日内相对低位
        if relative_position > max_relative_pos:
            return {'should_buy': False, 'strength': 0.0, 'reason': '价格位置过高'}

        # 条件2：MACD底背离信号
        if (latest['macd_dif'] > prev['macd_dif'] and
            latest['close'] <= prev['close']):
            should_buy = True
            strength += macd_weight
            reasons.append('MACD底背离')

        # 条件3：KDJ超卖反弹
        if latest['kdj_k'] < kdj_threshold and latest['kdj_k'] > prev['kdj_k']:
            should_buy = True
            strength += kdj_weight
            reasons.append('KDJ超卖反弹')

        # 条件4：价格接近近期低点且有反弹迹象
        recent_low = data['low'].tail(10).min()
        if latest['close'] <= recent_low * low_threshold and latest['close'] > latest['low']:
            should_buy = True
            strength += low_weight
            reasons.append('接近低点反弹')

        # 位置越低，强度越高
        strength += (max_relative_pos - relative_position) * 2

        return {
            'should_buy': should_buy,
            'strength': min(3.0, max(0.5, strength)),
            'reason': '; '.join(reasons) if reasons else '无明确原因'
        }

    def _detect_sell_signal(self, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测卖出信号"""
        should_sell = False
        strength = 1.0
        reasons = []

        # 从配置获取卖出信号参数
        sell_config = self.config.sell_signals
        min_relative_pos = sell_config.get('min_relative_position', 0.4)
        macd_weight = sell_config.get('macd_divergence_weight', 0.5)
        kdj_threshold = sell_config.get('kdj_overbought_threshold', 80)
        kdj_weight = sell_config.get('kdj_pullback_weight', 0.3)
        high_threshold = sell_config.get('near_high_threshold', 0.998)
        high_weight = sell_config.get('near_high_weight', 0.4)

        # 条件1：价格在日内相对高位
        if relative_position < min_relative_pos:
            return {'should_sell': False, 'strength': 0.0, 'reason': '价格位置过低'}

        # 条件2：MACD顶背离信号
        if (latest['macd_dif'] < prev['macd_dif'] and
            latest['close'] >= prev['close']):
            should_sell = True
            strength += macd_weight
            reasons.append('MACD顶背离')

        # 条件3：KDJ超买回调
        if latest['kdj_k'] > kdj_threshold and latest['kdj_k'] < prev['kdj_k']:
            should_sell = True
            strength += kdj_weight
            reasons.append('KDJ超买回调')

        # 条件4：价格接近近期高点且有回调迹象
        recent_high = data['high'].tail(10).max()
        if latest['close'] >= recent_high * high_threshold and latest['close'] < latest['high']:
            should_sell = True
            strength += high_weight
            reasons.append('接近高点回调')

        # 位置越高，强度越高
        strength += (relative_position - min_relative_pos) * 2

        return {
            'should_sell': should_sell,
            'strength': min(3.0, max(0.5, strength)),
            'reason': '; '.join(reasons) if reasons else '无明确原因'
        }

    def _should_skip_trade(self, symbol: str, current_price: float) -> bool:
        """检查是否应该跳过交易（防重复交易）"""
        current_time = datetime.now()
        if smart.is_back_test:
            current_time = str(smart.current_time)
            dt = datetime.strptime(current_time[:14], '%Y%m%d%H%M%S')
            milliseconds = len(current_time[14:]) > 0 and int(current_time[14:]) or 0
            current_time = dt.replace(microsecond=milliseconds * 1000)
        state = self.trade_state[symbol]

        # 检查交易间隔
        if state['last_buy_time'] and state['last_sell_time']:
            last_trade_time = max(state['last_buy_time'], state['last_sell_time'])
            if (current_time - last_trade_time).total_seconds() < self.config.min_trade_interval * 60:
                return True

        # 检查连续同方向交易
        if state['consecutive_same_direction'] >= self.config.max_consecutive_trades:
            return True

        # 检查价格变动
        if state['last_buy_price']:
            price_change = abs(current_price - state['last_buy_price']) / state['last_buy_price']
            if price_change < self.config.min_price_change:
                return True

        # 检查每日交易次数限制
        if self.daily_trades >= self.config.daily_max_trades:
            return True

        return False

class PositionManager:
    """仓位管理器 - 基于原T0T策略的多时间框架仓位管理"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.position_config = config.get("risk_management.position_management", {})

        # 仓位参数
        self.base_position_ratio = self.position_config.get("base_position_ratio", 0.5)
        self.min_position_ratio = self.position_config.get("min_position_ratio", 0.15)
        self.max_position_ratio = self.position_config.get("max_position_ratio", 0.85)

        # 调整限制
        self.monthly_max_increase = self.position_config.get("monthly_adjustment.max_increase", 0.1)
        self.monthly_max_decrease = self.position_config.get("monthly_adjustment.max_decrease", 0.1)
        self.weekly_max_increase = self.position_config.get("weekly_adjustment.max_increase", 0.06)
        self.weekly_max_decrease = self.position_config.get("weekly_adjustment.max_decrease", 0.06)
        self.daily_max_increase = self.position_config.get("daily_adjustment.max_increase", 0.04)
        self.daily_max_decrease = self.position_config.get("daily_adjustment.max_decrease", 0.04)

        # 当前仓位状态
        self.current_positions = {}  # 各股票当前仓位
        self.position_history = []   # 仓位历史记录

    def calculate_target_position(self, symbol: str, monthly_data: pd.DataFrame = None,
                                weekly_data: pd.DataFrame = None, daily_data: pd.DataFrame = None) -> float:
        """计算目标仓位"""
        try:
            # 如果没有足够的历史数据，使用基础仓位
            if (monthly_data is None or len(monthly_data) < 144 or
                weekly_data is None or len(weekly_data) < 20 or
                daily_data is None or len(daily_data) < 20):
                logger.info(f"{symbol} 数据不足，使用基础仓位: {self.base_position_ratio}")
                return self.base_position_ratio

            # 1. 计算月度目标仓位
            monthly_target = self._calculate_monthly_position(symbol, monthly_data)

            # 2. 基于周线调整
            weekly_target = self._calculate_weekly_adjustment(symbol, weekly_data, monthly_target)

            # 3. 基于日线微调
            daily_target = self._calculate_daily_adjustment(symbol, daily_data, weekly_target)

            # 4. 限制在允许范围内
            final_target = max(self.min_position_ratio,
                             min(self.max_position_ratio, daily_target))

            logger.info(f"{symbol} 仓位计算: 月度={monthly_target:.3f}, 周度={weekly_target:.3f}, "
                       f"日度={daily_target:.3f}, 最终={final_target:.3f}")

            return final_target

        except Exception as e:
            logger.error(f"计算 {symbol} 目标仓位失败: {e}")
            return self.base_position_ratio

    def _calculate_monthly_position(self, symbol: str, monthly_data: pd.DataFrame) -> float:
        """计算月度目标仓位"""
        current_data = monthly_data.iloc[-1]
        current_position = self.current_positions.get(symbol, self.base_position_ratio)

        # 检查144月均线突破
        if 'ma_144' in monthly_data.columns:
            ma144 = current_data['ma_144']
            close_price = current_data['close']

            # 跌破144月均线 - 加仓信号
            if close_price < ma144:
                # 检查是否首次跌破
                if len(monthly_data) > 1:
                    prev_data = monthly_data.iloc[-2]
                    if prev_data['close'] >= prev_data['ma_144']:
                        # 首次跌破，加仓至少50%
                        target = max(0.5, current_position + self.monthly_max_increase)
                        logger.info(f"{symbol} 首次跌破144月均线，加仓至 {target:.3f}")
                        return target

                # 持续跌破，继续加仓
                target = min(self.max_position_ratio, current_position + self.monthly_max_increase)
                return target

        # 检查5年高低点和背离
        if len(monthly_data) >= 60:  # 5年数据
            historical_high = monthly_data['high'].rolling(60).max().iloc[-1]
            historical_low = monthly_data['low'].rolling(60).min().iloc[-1]
            current_high = current_data['high']
            current_low = current_data['low']

            # 创5年新低且出现底背离 - 最大加仓
            if (current_low <= historical_low and
                'bullish_divergence' in current_data and current_data['bullish_divergence']):
                logger.info(f"{symbol} 创5年新低且底背离，加仓至最大仓位")
                return self.max_position_ratio

            # 创5年新高且出现顶背离 - 减仓
            if (current_high >= historical_high and
                'bearish_divergence' in current_data and current_data['bearish_divergence']):
                target = max(self.min_position_ratio, current_position - self.monthly_max_decrease)
                logger.info(f"{symbol} 创5年新高且顶背离，减仓至 {target:.3f}")
                return target

        return current_position

    def _calculate_weekly_adjustment(self, symbol: str, weekly_data: pd.DataFrame, monthly_target: float) -> float:
        """计算周度仓位调整"""
        current_data = weekly_data.iloc[-1]

        # 检查周线背离信号
        if 'bullish_divergence' in current_data and current_data['bullish_divergence']:
            # 底背离，在月度目标基础上加仓
            target = min(self.max_position_ratio, monthly_target + self.weekly_max_increase)
            logger.info(f"{symbol} 周线底背离，加仓至 {target:.3f}")
            return target

        if 'bearish_divergence' in current_data and current_data['bearish_divergence']:
            # 顶背离，在月度目标基础上减仓
            target = max(self.min_position_ratio, monthly_target - self.weekly_max_decrease)
            logger.info(f"{symbol} 周线顶背离，减仓至 {target:.3f}")
            return target

        return monthly_target

    def _calculate_daily_adjustment(self, symbol: str, daily_data: pd.DataFrame, weekly_target: float) -> float:
        """计算日度仓位微调"""
        current_data = daily_data.iloc[-1]

        # 检查日线背离信号
        if 'bullish_divergence' in current_data and current_data['bullish_divergence']:
            # 底背离，在周度目标基础上加仓
            target = min(self.max_position_ratio, weekly_target + self.daily_max_increase)
            logger.info(f"{symbol} 日线底背离，加仓至 {target:.3f}")
            return target

        if 'bearish_divergence' in current_data and current_data['bearish_divergence']:
            # 顶背离，在周度目标基础上减仓
            target = max(self.min_position_ratio, weekly_target - self.daily_max_decrease)
            logger.info(f"{symbol} 日线顶背离，减仓至 {target:.3f}")
            return target

        return weekly_target

    def update_position(self, symbol: str, new_position: float, reason: str = ""):
        """更新仓位"""
        old_position = self.current_positions.get(symbol, self.base_position_ratio)
        self.current_positions[symbol] = new_position

        # 记录仓位变化
        now = datetime.now()
        if smart.is_back_test:
            now = str(smart.current_time)
            dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
            milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
            now = dt.replace(microsecond=milliseconds * 1000)
        position_record = {
            'timestamp': now,
            'symbol': symbol,
            'old_position': old_position,
            'new_position': new_position,
            'change': new_position - old_position,
            'reason': reason
        }
        self.position_history.append(position_record)

        # 发送通知
        if abs(new_position - old_position) > 0.01:  # 变化超过1%才通知
            change_pct = (new_position - old_position) * 100
            notice_msg = (f"仓位调整: {symbol} "
                         f"{old_position:.1%} → {new_position:.1%} "
                         f"({change_pct:+.1f}%) "
                         f"原因: {reason}")
            self._send_notice(notice_msg)
            logger.info(notice_msg)

    def get_current_position(self, symbol: str) -> float:
        """获取当前仓位"""
        return self.current_positions.get(symbol, self.base_position_ratio)

    def _send_notice(self, message: str, level: str = 'info'):
        """发送通知（安全版本）"""
        try:
            # 使用正确的Smart API通知方法（字典参数格式）
            now = datetime.now()
            if smart.is_back_test:
                now = str(smart.current_time)
                dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
                milliseconds = int(now[14:])
                now = dt.replace(microsecond=milliseconds * 1000)
            params = {
                'level': level,  # open、info、success、warning、error
                'title': 'T0交易系统',
                'msg': message,
                'duration': 4500,
                'timestamp': now.strftime("%Y-%m-%d %H:%M:%S")
            }

            # 由于通知功能可能有问题，先记录日志
            logger.info(f"准备发送通知: {message} (级别: {level})")

            # 尝试发送通知
            try:
                smart.notice(params)
                logger.info(f"通知发送成功: {message}")
            except Exception as e:
                logger.warning(f"通知发送失败: {e}")

        except Exception as e:
            logger.warning(f"发送通知异常，但不影响系统运行: {e}")
            # 通知失败不影响系统运行

class RiskManager:
    """风险管理器"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.daily_pnl = 0.0  # 当日盈亏
        self.max_drawdown = 0.0  # 最大回撤
        self.consecutive_losses = 0  # 连续亏损次数
        self.order_failures = 0  # 下单失败次数
        self.total_trades = 0  # 总交易次数
        self.winning_trades = 0  # 盈利交易次数

    def check_trading_allowed(self, symbol: str, action: str, price: float, volume: int) -> Dict:
        """检查是否允许交易"""

        # 检查每日最大交易次数
        if self.total_trades >= self.config.daily_max_trades:
            return {
                'allowed': False,
                'reason': f'已达到每日最大交易次数限制: {self.config.daily_max_trades}'
            }

        # 检查每日最大亏损
        max_daily_loss = config.get('monitoring.performance_alerts.max_daily_loss', 0.05)
        if self.daily_pnl < -max_daily_loss:
            return {
                'allowed': False,
                'reason': f'已达到每日最大亏损限制: {max_daily_loss*100:.1f}%'
            }

        # 检查连续亏损次数
        max_consecutive_losses = config.get('monitoring.performance_alerts.max_consecutive_losses', 5)
        if self.consecutive_losses >= max_consecutive_losses:
            return {
                'allowed': False,
                'reason': f'连续亏损次数过多: {self.consecutive_losses}'
            }

        # 检查下单失败次数
        max_order_failures = config.get('monitoring.system_alerts.max_order_failures', 10)
        if self.order_failures >= max_order_failures:
            return {
                'allowed': False,
                'reason': f'下单失败次数过多: {self.order_failures}'
            }

        # 检查交易时间
        if not self._is_trading_time():
            return {
                'allowed': False,
                'reason': '当前不在交易时间内'
            }

        return {'allowed': True, 'reason': '风险检查通过'}

    def _is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        now = datetime.now()
        if smart.is_back_test:
            now = str(smart.current_time)
            dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
            milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
            now = dt.replace(microsecond=milliseconds * 1000)
        current_time = now.strftime('%H:%M')

        trading_hours = config.get_trading_hours()
        morning_start = trading_hours.get('morning_start', '09:30')
        morning_end = trading_hours.get('morning_end', '11:30')
        afternoon_start = trading_hours.get('afternoon_start', '13:00')
        afternoon_end = trading_hours.get('afternoon_end', '15:00')

        # 检查是否在交易时间段内
        in_morning = morning_start <= current_time <= morning_end
        in_afternoon = afternoon_start <= current_time <= afternoon_end

        return in_morning or in_afternoon

    def should_force_close(self) -> bool:
        """检查是否应该强制平仓"""
        now = datetime.now()
        if smart.is_back_test:
            now = str(smart.current_time)
            dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
            milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
            now = dt.replace(microsecond=milliseconds * 1000)
        current_time = now.strftime('%H:%M')
        force_close_time = config.get_force_close_time()

        return current_time >= force_close_time

    def record_trade_result(self, symbol: str, side: str, pnl: float):
        """记录交易结果"""
        self.total_trades += 1
        self.daily_pnl += pnl

        if pnl > 0:
            self.winning_trades += 1
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1

        # 更新最大回撤
        if self.daily_pnl < self.max_drawdown:
            self.max_drawdown = self.daily_pnl

        logger.info(f"交易记录: {symbol} {side} 盈亏:{pnl:.2f} 当日总盈亏:{self.daily_pnl:.2f}")

    def record_order_failure(self):
        """记录下单失败"""
        self.order_failures += 1
        logger.warning(f"下单失败次数: {self.order_failures}")

    def get_risk_metrics(self) -> Dict:
        """获取风险指标"""
        win_rate = self.winning_trades / max(1, self.total_trades)

        return {
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'consecutive_losses': self.consecutive_losses,
            'order_failures': self.order_failures
        }

    def reset_daily_stats(self):
        """重置每日统计"""
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.order_failures = 0
        self.total_trades = 0
        self.winning_trades = 0
        logger.info("每日风险统计已重置")

class TradeExecutor:
    """交易执行器"""

    def __init__(self, config: T0TradingConfig, risk_manager: RiskManager):
        self.config = config
        self.risk_manager = risk_manager
        self.pending_orders = {}  # 待处理订单
        self.executed_trades = []  # 已执行交易记录

    def execute_trade(self, symbol: str, action: str, price: float, strength: float, reason: str):
        """执行交易"""
        try:
            # 计算交易数量
            volume = self._calculate_trade_volume(symbol, strength)
            if volume <= 0:
                logger.warning(f"交易数量为0，跳过交易: {symbol} {action}")
                return False

            # 风险检查
            risk_check = self.risk_manager.check_trading_allowed(symbol, action, price, volume)
            if not risk_check['allowed']:
                logger.warning(f"风险检查未通过: {risk_check['reason']}")
                return False

            # 确定交易方向
            side = smart.Type.Side.Buy if action == 'buy' else smart.Type.Side.Sell

            # 下单前通知
            action_cn = "买入" if action == 'buy' else "卖出"
            pre_trade_msg = (f"准备{action_cn}: {symbol} "
                           f"{volume}股 @{price:.2f} "
                           f"强度:{strength:.1f} "
                           f"原因:{reason}")
            self._send_notice(pre_trade_msg)
            logger.info(pre_trade_msg)

            # 下单
            order_id = self._place_order(symbol, side, price, volume, reason)

            if order_id:
                # 交易成功通知
                success_msg = (f"✅ {action_cn}成功: {symbol} "
                             f"{volume}股 @{price:.2f} "
                             f"订单号:{order_id} "
                             f"原因:{reason}")
                self._send_notice(success_msg, 'success')
                logger.info(success_msg)
                return True
            else:
                # 交易失败通知
                fail_msg = f"❌ {action_cn}失败: {symbol} {volume}股 @{price:.2f}"
                self._send_notice(fail_msg, 'error')
                logger.error(fail_msg)
                self.risk_manager.record_order_failure()
                return False

        except Exception as e:
            error_msg = f"❌ 交易执行异常: {symbol} {action} - {str(e)}"
            self._send_notice(error_msg, 'error')
            logger.error(error_msg)
            self.risk_manager.record_order_failure()
            return False

    def _calculate_trade_volume(self, symbol: str, strength: float) -> int:
        """计算交易数量"""
        # 这里需要根据实际持仓和资金情况计算
        # 暂时使用固定数量，实际应用中需要动态计算
        base_volume = 100  # 基础交易量（手）
        volume = int(base_volume * strength)

        # 确保是100的整数倍（A股最小交易单位）
        volume = (volume // 100) * 100

        return max(100, volume)  # 最小100股

    def _place_order(self, symbol: str, side, price: float, volume: int, reason: str) -> Optional[str]:
        """下单"""
        try:
            now = datetime.now()
            if smart.is_back_test:
                now = str(smart.current_time)
                dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
                milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
                now = dt.replace(microsecond=milliseconds * 1000)
            order_id = f"T0_{symbol}_{now.strftime('%H%M%S')}"

            def order_callback(order, err):
                if err:
                    logger.error(f"下单失败: {err}")
                else:
                    logger.info(f"下单成功: {smart.utils.toString(order)}")
                    self.pending_orders[order_id] = {
                        'symbol': symbol,
                        'side': side,
                        'price': price,
                        'volume': volume,
                        'reason': reason,
                        'timestamp': now,
                        'order': order
                    }

            # 使用Smart API下单 - 按照官方示例格式，添加strategy相关参数
            smart.insert_order(
                account_id=None,  # 使用默认账户
                strategy_platform_type=smart.Type.StrategyPlatformType.FrontPy,  # 策略平台类型
                strategy_id=None,  # 策略ID，使用默认
                instrument_id=symbol,
                exchange_id=smart.Type.Exchange.SZE if symbol.endswith('.SZ') else smart.Type.Exchange.SSE,
                limit_price=price,
                volume=volume,
                price_type=smart.Type.PriceType.Limit,  # 限价单
                side=side,
                offset=smart.Type.Offset.Init,  # 开仓
                order_client_id=0,  # 客户自定义ID
                parent_order_id="",  # 母单编号
                business_type=smart.Type.BusinessType.CASH,  # 现金交易
                callback=order_callback
            )

            return order_id

        except Exception as e:
            logger.error(f"下单时发生错误: {e}")
            return None



    def on_order_update(self, order):
        """订单状态更新回调"""
        try:
            logger.info(f"订单更新: {smart.utils.toString(order)}")
            # 这里可以添加订单状态处理逻辑
        except Exception as e:
            logger.error(f"处理订单更新时发生错误: {e}")

    def on_trade_update(self, trade):
        """成交回调"""
        try:
            logger.info(f"成交通知: {smart.utils.toString(trade)}")

            # 记录成交信息
            now = datetime.now()
            if smart.is_back_test:
                now = str(smart.current_time)
                dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
                milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
                now = dt.replace(microsecond=milliseconds * 1000)
            trade_record = {
                'symbol': trade.instrument_id,
                'side': 'buy' if trade.side == smart.Type.Side.Buy else 'sell',
                'price': trade.price,
                'volume': trade.volume,
                'timestamp': now,
                'trade_id': trade.trade_id
            }

            self.executed_trades.append(trade_record)

        except Exception as e:
            logger.error(f"处理成交通知时发生错误: {e}")

class T0TradingSystem:
    """T0交易系统主控制器"""

    def __init__(self):
        self.config = T0TradingConfig()
        self.data_manager = MarketDataManager(self.config)
        self.strategy = T0TradingStrategy(self.config)
        self.position_manager = PositionManager(self.config)
        self.risk_manager = RiskManager(self.config)
        self.executor = TradeExecutor(self.config, self.risk_manager)
        self.is_running = False
        self._skip_quote_processing = False  # 标志位：是否跳过quote处理回调

        # 设置数据回调
        self.data_manager.add_data_callback(self.on_market_data_update)

    def start(self):
        """启动交易系统"""
        try:
            logger.info("启动T0交易系统...")

            # 发送启动通知
            start_msg = f"🚀 T0交易系统启动中... 目标股票: {', '.join(self.config.target_symbols)}"
            self._send_notice(start_msg, 'info')

            # 1. 加载warm-up数据
            logger.info("开始加载warm-up数据...")
            self.data_manager.load_warmup_data(self.config.target_symbols)

            # 2. 订阅行情数据
            self.data_manager.subscribe_quotes(self.config.target_symbols)

            # 3. 设置Smart API回调
            try:
                # 先将策略和执行器传递给数据管理器，确保回调中能够使用
                self.data_manager.strategy = self.strategy
                self.data_manager.executor = self.executor

                # 设置全局引用，供回调函数使用
                global _trading_system_instance
                _trading_system_instance = self

                # 设置订单和成交回调（使用Smart API标准方式）
                if hasattr(smart, 'current_account') and smart.current_account:
                    smart.current_account.on_order(_global_order_callback)
                    smart.current_account.on_trade(_global_trade_callback)
                    logger.info("订单和成交回调设置完成")

                # 设置行情回调 - 使用Smart API标准事件方式
                smart.on(smart.Event.ON_QUOTE, _global_quote_callback)
                logger.info("行情回调设置完成 - 系统开始持续处理行情数据")

            except Exception as e:
                logger.warning(f"设置Smart API回调时出现问题: {e}")
                # 回调设置失败不影响系统启动

            self.is_running = True

            # 发送启动成功通知
            success_msg = "✅ T0交易系统启动成功！"
            self._send_notice(success_msg, 'success')
            logger.info(success_msg)

        except Exception as e:
            error_msg = f"❌ 启动T0交易系统失败: {e}"
            self._send_notice(error_msg)
            logger.error(error_msg)
            self.is_running = False

    def stop(self):
        """停止交易系统"""
        self.is_running = False
        logger.info("T0交易系统已停止")

    def on_market_data_update(self, symbol: str, quote_data: Dict):
        """市场数据更新回调 - 现在主要策略通过on_quote触发，这里只保留强制平仓检查"""
        if not self.is_running:
            return

        # 如果设置了跳过quote处理标志，则跳过后续处理
        if self._skip_quote_processing:
            logger.debug(f"跳过 {symbol} 的quote处理回调（已设置跳过标志）")
            return

        try:
            # 检查是否需要强制平仓（这是唯一需要保留的检查）
            if self.risk_manager.should_force_close():
                self._force_close_positions()
                return

        except Exception as e:
            logger.error(f"处理市场数据更新时发生错误: {e}")

    def _force_close_positions(self):
        """强制平仓 - 使用Smart API获取实际持仓信息"""
        logger.warning("触发强制平仓时间，开始平仓操作...")

        try:
            # 使用Smart API获取当前持仓
            if hasattr(smart, 'current_account') and smart.current_account:
                positions = smart.current_account.get_position()

                if positions:
                    logger.info(f"获取到 {len(positions)} 个持仓，开始强制平仓")
                    # 有持仓时，重置跳过标志，允许正常处理
                    self._skip_quote_processing = False

                    for position in positions:
                        try:
                            # 获取持仓信息
                            symbol = position.get('instrument_id', '')
                            current_qty = position.get('total_qty', 0)
                            available_qty = position.get('sellable_qty', 0)

                            if available_qty > 0:
                                # 获取当前价格
                                current_price = None
                                if symbol in self.data_manager.quote_data:
                                    current_price = self.data_manager.quote_data[symbol]['last_price']
                                else:
                                    # 如果没有实时价格，使用持仓成本价作为参考
                                    current_price = position.get('avg_price', 0)

                                if current_price and current_price > 0:
                                    logger.info(f"强制平仓 {symbol}: 持仓{current_qty}, 可卖{available_qty}, 价格{current_price}")

                                    # 执行强制卖出
                                    success = self.executor.execute_trade(
                                        symbol=symbol,
                                        action='sell',
                                        price=current_price,
                                        strength=3.0,  # 最高强度
                                        reason='强制平仓',
                                        volume=available_qty  # 全部卖出
                                    )

                                    if success:
                                        logger.info(f"✅ {symbol} 强制平仓成功")
                                    else:
                                        logger.error(f"❌ {symbol} 强制平仓失败")
                                else:
                                    logger.warning(f"⚠️ {symbol} 无法获取有效价格，跳过平仓")
                            else:
                                logger.info(f"ℹ️ {symbol} 无可卖持仓，跳过")

                        except Exception as e:
                            logger.error(f"处理持仓平仓失败 {symbol}: {e}")
                else:
                    logger.info("当前无持仓，无需强制平仓")
                    # 设置标志，跳过后续的quote处理回调以避免不必要的处理
                    self._skip_quote_processing = True
                    logger.debug("已设置跳过quote处理标志，避免不必要的回调执行")
            else:
                logger.error("无法获取Smart API账户信息，强制平仓失败")

        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            # 降级处理：对配置的股票进行强制平仓尝试
            logger.warning("降级处理：对配置股票进行强制平仓尝试")
            for symbol in self.config.target_symbols:
                try:
                    if symbol in self.data_manager.quote_data:
                        current_price = self.data_manager.quote_data[symbol]['last_price']
                        self.executor.execute_trade(
                            symbol=symbol,
                            action='sell',
                            price=current_price,
                            strength=3.0,
                            reason='强制平仓(降级)'
                        )
                except Exception as fallback_error:
                    logger.error(f"降级强制平仓失败 {symbol}: {fallback_error}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        risk_metrics = self.risk_manager.get_risk_metrics()

        return {
            'is_running': self.is_running,
            'target_symbols': self.config.target_symbols,
            'risk_metrics': risk_metrics,
            'data_status': {
                'subscribed_symbols': list(self.data_manager.quote_data.keys()),
                'minute_data_count': {
                    symbol: len(data) for symbol, data in self.data_manager.minute_data.items()
                }
            }
        }



    def _update_trade_state(self, symbol: str, signal: Dict):
        """更新交易状态"""
        state = self.strategy.trade_state[symbol]
        current_time = datetime.now()
        if smart.is_back_test:
            current_time = str(smart.current_time)
            dt = datetime.strptime(current_time[:14], '%Y%m%d%H%M%S')
            milliseconds = len(current_time[14:]) > 0 and int(current_time[14:]) or 0
            current_time = dt.replace(microsecond=milliseconds * 1000)

        if signal['action'] == 'buy':
            state['last_buy_price'] = signal['price']
            state['last_buy_time'] = current_time

            if state['last_trade_direction'] == 'buy':
                state['consecutive_same_direction'] += 1
            else:
                state['consecutive_same_direction'] = 1
            state['last_trade_direction'] = 'buy'

        elif signal['action'] == 'sell':
            state['last_sell_price'] = signal['price']
            state['last_sell_time'] = current_time

            if state['last_trade_direction'] == 'sell':
                state['consecutive_same_direction'] += 1
            else:
                state['consecutive_same_direction'] = 1
            state['last_trade_direction'] = 'sell'

        # 增加当日交易次数
        self.strategy.daily_trades += 1
    

    def _send_notice(self, message: str, level: str = 'info'):
        """发送通知（安全版本）"""
        try:
            # 使用正确的Smart API通知方法（字典参数格式）
            now = datetime.now()
            if smart.is_back_test:
                now = str(smart.current_time)
                dt = datetime.strptime(now[:14], '%Y%m%d%H%M%S')
                milliseconds = len(now[14:]) > 0 and int(now[14:]) or 0
                now = dt.replace(microsecond=milliseconds * 1000)
            params = {
                'level': level,  # open、info、success、warning、error
                'title': 'T0交易系统',
                'msg': message,
                'duration': 4500,
                'timestamp': now.strftime("%Y-%m-%d %H:%M:%S")
            }

            # 由于通知功能可能有问题，先记录日志
            logger.info(f"准备发送通知: {message} (级别: {level})")

            # 尝试发送通知
            try:
                smart.notice(params)
                logger.info(f"通知发送成功: {message}")
            except Exception as e:
                logger.warning(f"通知发送失败: {e}")
        except Exception as e:
            logger.warning(f"发送通知异常，但不影响系统运行: {e}")
            # 通知失败不影响系统运行
# 全局交易系统实例引用
_trading_system_instance = None

# Smart API标准回调函数（必须是普通函数，不能是类方法）
def _global_quote_callback(quote):
    """全局行情回调函数"""
    try:
        if _trading_system_instance and _trading_system_instance.data_manager:
            _trading_system_instance.data_manager.on_quote_received(quote)
    except Exception as e:
        logger.error(f"处理行情回调时发生错误: {e}")

def _global_order_callback(order):
    """全局订单回调函数"""
    try:
        if _trading_system_instance and _trading_system_instance.executor:
            _trading_system_instance.executor.on_order_update(order)
    except Exception as e:
        logger.error(f"处理订单回调时发生错误: {e}")

def _global_trade_callback(trade):
    """全局成交回调函数"""
    try:
        if _trading_system_instance and _trading_system_instance.executor:
            _trading_system_instance.executor.on_trade_update(trade)
    except Exception as e:
        logger.error(f"处理成交回调时发生错误: {e}")

# 全局交易系统实例
trading_system = T0TradingSystem()

def init():
    """Smart平台初始化回调"""
    logger.info("Smart平台初始化...")

    # 启动T0交易系统
    trading_system.start()

def show():
    """显示回调"""
    logger.info("显示T0交易系统界面")

def hide():
    """隐藏回调"""
    logger.info("隐藏T0交易系统界面")

def close():
    """关闭回调"""
    logger.info("关闭T0交易系统")
    trading_system.stop()

# 注册Smart平台回调（仅在Smart API可用时）
smart.on_init(init)
smart.on_show(show)
smart.on_hide(hide)
smart.on_close(close)