"""
T0T_Online - 基于Smart平台的T0交易策略系统
主要功能：
1. 实时行情数据获取
2. 技术指标计算
3. T0交易信号检测
4. 自动交易执行
5. 风险管理
"""

from smart import *
import logging
import pandas as pd
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Callable
from config_loader import config

class TradingDayCalculator:
    """交易日计算工具"""

    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（简化版本，排除周末）"""
        # 周一到周五为交易日（这里简化处理，实际应该考虑节假日）
        return date.weekday() < 5

    @staticmethod
    def get_previous_trading_days(count: int, from_date: datetime = None) -> List[datetime]:
        """获取前N个交易日"""
        if from_date is None:
            from_date = datetime.now()

        trading_days = []
        current_date = from_date - timedelta(days=1)  # 从前一天开始

        while len(trading_days) < count:
            if TradingDayCalculator.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date -= timedelta(days=1)

        return trading_days

    @staticmethod
    def get_trading_day_range(days_back: int, from_date: datetime = None) -> tuple:
        """获取交易日范围（开始日期，结束日期）"""
        if from_date is None:
            from_date = datetime.now()

        trading_days = TradingDayCalculator.get_previous_trading_days(days_back, from_date)
        if trading_days:
            start_date = trading_days[-1]  # 最早的交易日
            end_date = trading_days[0]     # 最近的交易日
            return start_date, end_date
        else:
            # 如果没有找到交易日，返回默认范围
            end_date = from_date - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back)
            return start_date, end_date

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger = logging.getLogger(__name__)

class T0TradingConfig:
    """T0交易配置"""
    def __init__(self):
        # 从配置文件加载参数
        trading_config = config.get_trading_config()
        indicators_config = config.get_indicators_config()
        risk_config = config.get_risk_management_config()
        signal_config = config.get_signal_detection_config()

        # 交易标的配置
        self.target_symbols = config.get_target_symbols()

        # T0交易参数
        self.min_trade_portion = trading_config.get('min_trade_portion', 1/8)
        self.max_trade_portion = trading_config.get('max_trade_portion', 1/3)
        self.price_threshold = trading_config.get('price_threshold', 0.01)
        self.transaction_cost_rate = trading_config.get('transaction_cost_rate', 0.0014)

        # 防重复交易参数
        self.min_trade_interval = trading_config.get('min_trade_interval', 3)
        self.max_consecutive_trades = trading_config.get('max_consecutive_trades', 2)
        self.min_price_change = trading_config.get('min_price_change', 0.002)

        # 技术指标参数
        macd_config = indicators_config.get('macd', {})
        self.macd_fast = macd_config.get('fast_period', 12)
        self.macd_slow = macd_config.get('slow_period', 26)
        self.macd_signal = macd_config.get('signal_period', 9)

        kdj_config = indicators_config.get('kdj', {})
        self.kdj_period = kdj_config.get('period', 9)

        # 风险管理参数
        self.max_position_ratio = risk_config.get('max_position_ratio', 0.8)
        self.stop_loss_ratio = risk_config.get('stop_loss_ratio', 0.05)
        self.daily_max_trades = risk_config.get('daily_max_trades', 20)

        # 信号检测参数
        self.buy_signals = signal_config.get('buy_signals', {})
        self.sell_signals = signal_config.get('sell_signals', {})

        # 存储原始配置以支持get方法
        self._config_data = {
            'trading': trading_config,
            'indicators': indicators_config,
            'risk_management': risk_config,
            'signal_detection': signal_config
        }

    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        try:
            keys = key_path.split('.')
            current = self._config_data

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default

            return current
        except Exception:
            return default

class TechnicalIndicators:
    """技术指标计算类"""

    @staticmethod
    def calculate_macd(data: pd.DataFrame, fast=12, slow=26, signal=9) -> pd.DataFrame:
        """计算MACD指标"""
        df = data.copy()

        # 计算EMA
        df['ema_fast'] = df['close'].ewm(span=fast, adjust=False).mean()
        df['ema_slow'] = df['close'].ewm(span=slow, adjust=False).mean()

        # 计算MACD
        df['macd_dif'] = df['ema_fast'] - df['ema_slow']
        df['macd_dea'] = df['macd_dif'].ewm(span=signal, adjust=False).mean()
        df['macd_bar'] = 2 * (df['macd_dif'] - df['macd_dea'])

        # 删除中间计算列
        df = df.drop(['ema_fast', 'ema_slow'], axis=1)

        return df

    @staticmethod
    def calculate_kdj(data: pd.DataFrame, period=9) -> pd.DataFrame:
        """计算KDJ指标"""
        df = data.copy()

        # 计算RSV
        low_min = df['low'].rolling(window=period).min()
        high_max = df['high'].rolling(window=period).max()
        df['rsv'] = (df['close'] - low_min) / (high_max - low_min) * 100

        # 计算KDJ
        df['kdj_k'] = df['rsv'].ewm(alpha=1/3, adjust=False).mean()
        df['kdj_d'] = df['kdj_k'].ewm(alpha=1/3, adjust=False).mean()
        df['kdj_j'] = 3 * df['kdj_k'] - 2 * df['kdj_d']

        return df

class MarketDataManager:
    """市场数据管理器"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.quote_data = {}  # 存储最新行情数据
        self.minute_data = {}  # 存储分钟级数据
        self.data_callbacks = []  # 数据回调函数列表
        self.is_warmed_up = {}  # 记录各股票是否已完成warm-up
        self.warmup_required_periods = 50  # warm-up需要的数据周期数

    def subscribe_quotes(self, symbols: List[str]):
        """订阅行情数据"""
        try:
            # 订阅实时行情
            smart.smart.subscribe(symbols)
            logger.info(f"已订阅行情数据: {symbols}")

            # 为每个股票初始化warm-up状态
            for symbol in symbols:
                self.is_warmed_up[symbol] = False

        except Exception as e:
            logger.error(f"订阅行情数据失败: {e}")

    def load_warmup_data(self, symbols: List[str]):
        """加载warm-up数据（前两个交易日的分钟数据）"""
        try:
            for symbol in symbols:
                logger.info(f"开始加载 {symbol} 的warm-up数据...")

                # 使用交易日计算工具获取前两个交易日
                try:
                    # 获取前两个交易日的日期范围
                    start_date, end_date = TradingDayCalculator.get_trading_day_range(2)

                    logger.info(f"获取 {symbol} 从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的数据")

                    # 使用Smart API获取历史数据
                    historical_data = smart.smart.query_data_async(
                        symbol,
                        start_date.strftime('%Y%m%d'),
                        end_date.strftime('%Y%m%d'),
                        '1m'
                    )

                    if historical_data and len(historical_data) > 0:
                        # 初始化分钟数据存储
                        if symbol not in self.minute_data:
                            self.minute_data[symbol] = deque(maxlen=1000)

                        # 将历史数据转换为标准格式并存储
                        for data_point in historical_data:
                            minute_bar = {
                                'timestamp': data_point.get('timestamp', datetime.now()),
                                'open': data_point.get('open', 0),
                                'high': data_point.get('high', 0),
                                'low': data_point.get('low', 0),
                                'close': data_point.get('close', 0),
                                'volume': data_point.get('volume', 0)
                            }
                            self.minute_data[symbol].append(minute_bar)

                        # 检查是否有足够的数据进行warm-up
                        if len(self.minute_data[symbol]) >= self.warmup_required_periods:
                            self.is_warmed_up[symbol] = True
                            logger.info(f"{symbol} warm-up完成，加载了 {len(self.minute_data[symbol])} 条历史数据")
                        else:
                            logger.warning(f"{symbol} warm-up数据不足，仅加载了 {len(self.minute_data[symbol])} 条数据")
                    else:
                        logger.warning(f"无法获取 {symbol} 的历史数据")

                except Exception as api_error:
                    logger.error(f"获取 {symbol} 历史数据失败: {api_error}")
                    # 如果无法获取历史数据，标记为需要实时积累
                    self.is_warmed_up[symbol] = False

        except Exception as e:
            logger.error(f"加载warm-up数据失败: {e}")

    def is_symbol_ready_for_trading(self, symbol: str) -> bool:
        """检查股票是否准备好进行交易（已完成warm-up）"""
        return self.is_warmed_up.get(symbol, False)

    def on_quote_received(self, quote):
        """行情数据回调"""
        try:
            symbol = quote.code

            # 更新最新行情
            self.quote_data[symbol] = {
                'symbol': symbol,
                'last_price': quote.last_price,
                'volume': quote.volume,
                'turnover': quote.turnover,
                'bid_price': quote.bid_price[0] if quote.bid_price else 0,
                'ask_price': quote.ask_price[0] if quote.ask_price else 0,
                'timestamp': datetime.now()
            }

            # 更新分钟数据
            self._update_minute_data(symbol, quote)

            # 通知所有回调函数
            for callback in self.data_callbacks:
                callback(symbol, self.quote_data[symbol])

        except Exception as e:
            logger.error(f"处理行情数据失败: {e}")

    def _update_minute_data(self, symbol: str, quote):
        """更新分钟级数据"""
        current_time = datetime.now()
        minute_key = current_time.replace(second=0, microsecond=0)

        if symbol not in self.minute_data:
            self.minute_data[symbol] = deque(maxlen=1000)  # 保留最近1000分钟数据

        # 检查是否需要创建新的分钟K线
        if (not self.minute_data[symbol] or
            self.minute_data[symbol][-1]['timestamp'] < minute_key):

            # 创建新的分钟K线
            minute_bar = {
                'timestamp': minute_key,
                'open': quote.last_price,
                'high': quote.last_price,
                'low': quote.last_price,
                'close': quote.last_price,
                'volume': quote.volume
            }
            self.minute_data[symbol].append(minute_bar)

            # 检查是否达到warm-up要求
            if (not self.is_warmed_up.get(symbol, False) and
                len(self.minute_data[symbol]) >= self.warmup_required_periods):
                self.is_warmed_up[symbol] = True
                logger.info(f"{symbol} 通过实时数据积累完成warm-up")
        else:
            # 更新当前分钟K线
            current_bar = self.minute_data[symbol][-1]
            current_bar['high'] = max(current_bar['high'], quote.last_price)
            current_bar['low'] = min(current_bar['low'], quote.last_price)
            current_bar['close'] = quote.last_price
            current_bar['volume'] = quote.volume

    def setup_minute_timer(self):
        """设置分钟级定时器，确保准确的分钟数据"""
        try:
            # 使用Smart API的定时器功能
            smart.smart.add_time_interval(60, self._on_minute_timer)
            logger.info("分钟级定时器已设置")
        except Exception as e:
            logger.error(f"设置分钟级定时器失败: {e}")
            # 如果定时器设置失败，可以考虑使用其他方式

    def _on_minute_timer(self):
        """分钟级定时器回调"""
        try:
            current_time = datetime.now()

            # 只在交易时间内处理
            if not self._is_trading_time():
                return

            # 为每个订阅的股票获取最新数据
            for symbol in self.quote_data.keys():
                try:
                    # 使用query_market_data获取最新的分钟数据
                    latest_data = smart.query_market_data(
                        symbol=symbol,
                        period='1m',
                        count=1
                    )

                    if latest_data and len(latest_data) > 0:
                        data_point = latest_data[0]
                        minute_key = current_time.replace(second=0, microsecond=0)

                        if symbol not in self.minute_data:
                            self.minute_data[symbol] = deque(maxlen=1000)

                        # 检查是否需要创建新的分钟K线
                        if (not self.minute_data[symbol] or
                            self.minute_data[symbol][-1]['timestamp'] < minute_key):

                            minute_bar = {
                                'timestamp': minute_key,
                                'open': data_point.get('open', 0),
                                'high': data_point.get('high', 0),
                                'low': data_point.get('low', 0),
                                'close': data_point.get('close', 0),
                                'volume': data_point.get('volume', 0)
                            }
                            self.minute_data[symbol].append(minute_bar)

                            # 检查warm-up状态
                            if (not self.is_warmed_up.get(symbol, False) and
                                len(self.minute_data[symbol]) >= self.warmup_required_periods):
                                self.is_warmed_up[symbol] = True
                                logger.info(f"{symbol} 通过定时器数据完成warm-up")

                except Exception as symbol_error:
                    logger.error(f"获取 {symbol} 分钟数据失败: {symbol_error}")

        except Exception as e:
            logger.error(f"分钟级定时器处理失败: {e}")

    def _is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')

        trading_hours = config.get_trading_hours()
        morning_start = trading_hours.get('morning_start', '09:30')
        morning_end = trading_hours.get('morning_end', '11:30')
        afternoon_start = trading_hours.get('afternoon_start', '13:00')
        afternoon_end = trading_hours.get('afternoon_end', '15:00')

        # 检查是否在交易时间段内
        in_morning = morning_start <= current_time <= morning_end
        in_afternoon = afternoon_start <= current_time <= afternoon_end

        return in_morning or in_afternoon

    def get_minute_data(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """获取分钟级数据"""
        if symbol not in self.minute_data or len(self.minute_data[symbol]) == 0:
            return pd.DataFrame()

        # 转换为DataFrame
        data = list(self.minute_data[symbol])[-periods:]
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)

        return df

    def add_data_callback(self, callback: Callable):
        """添加数据回调函数"""
        self.data_callbacks.append(callback)

class T0TradingStrategy:
    """T0交易策略核心类"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.positions = {}  # 持仓信息
        self.trade_state = {}  # 交易状态
        self.daily_trades = 0  # 当日交易次数
        self.total_profit = 0.0  # 累计收益

        # 初始化交易状态
        for symbol in config.target_symbols:
            self.trade_state[symbol] = {
                'last_buy_price': None,
                'last_sell_price': None,
                'last_buy_time': None,
                'last_sell_time': None,
                'daily_high': None,
                'daily_low': None,
                'consecutive_same_direction': 0,
                'last_trade_direction': None
            }

    def analyze_market_data(self, symbol: str, minute_data: pd.DataFrame) -> Dict:
        """分析市场数据，生成交易信号"""
        if len(minute_data) < 30:  # 数据不足
            return {'action': 'hold', 'strength': 0.0, 'reason': '数据不足'}

        # 计算技术指标
        data_with_indicators = TechnicalIndicators.calculate_macd(minute_data,
                                                                 self.config.macd_fast,
                                                                 self.config.macd_slow,
                                                                 self.config.macd_signal)
        data_with_indicators = TechnicalIndicators.calculate_kdj(data_with_indicators,
                                                               self.config.kdj_period)

        # 获取最新数据
        latest = data_with_indicators.iloc[-1]
        prev = data_with_indicators.iloc[-2] if len(data_with_indicators) > 1 else latest

        # 更新日内高低点
        current_high = minute_data['high'].max()
        current_low = minute_data['low'].min()

        if self.trade_state[symbol]['daily_high'] is None:
            self.trade_state[symbol]['daily_high'] = current_high
            self.trade_state[symbol]['daily_low'] = current_low
        else:
            self.trade_state[symbol]['daily_high'] = max(self.trade_state[symbol]['daily_high'], current_high)
            self.trade_state[symbol]['daily_low'] = min(self.trade_state[symbol]['daily_low'], current_low)

        # 计算相对位置
        daily_range = self.trade_state[symbol]['daily_high'] - self.trade_state[symbol]['daily_low']
        if daily_range > 0:
            relative_position = (latest['close'] - self.trade_state[symbol]['daily_low']) / daily_range
        else:
            relative_position = 0.5

        # 检测T0交易信号
        signal = self._detect_t0_signals(symbol, latest, prev, relative_position, data_with_indicators)

        return signal

    def _detect_t0_signals(self, symbol: str, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测T0交易信号"""

        # 检查是否应该跳过交易（防重复交易）
        if self._should_skip_trade(symbol, latest['close']):
            return {'action': 'hold', 'strength': 0.0, 'reason': '防重复交易'}

        # T0买入信号检测
        buy_signal = self._detect_buy_signal(latest, prev, relative_position, data)
        if buy_signal['should_buy']:
            return {
                'action': 'buy',
                'strength': buy_signal['strength'],
                'reason': buy_signal['reason'],
                'price': latest['close']
            }

        # T0卖出信号检测
        sell_signal = self._detect_sell_signal(latest, prev, relative_position, data)
        if sell_signal['should_sell']:
            return {
                'action': 'sell',
                'strength': sell_signal['strength'],
                'reason': sell_signal['reason'],
                'price': latest['close']
            }

        return {'action': 'hold', 'strength': 0.0, 'reason': '无明确信号'}

    def _detect_buy_signal(self, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测买入信号"""
        should_buy = False
        strength = 1.0
        reasons = []

        # 从配置获取买入信号参数
        buy_config = self.config.buy_signals
        max_relative_pos = buy_config.get('max_relative_position', 0.6)
        macd_weight = buy_config.get('macd_divergence_weight', 0.5)
        kdj_threshold = buy_config.get('kdj_oversold_threshold', 20)
        kdj_weight = buy_config.get('kdj_rebound_weight', 0.3)
        low_threshold = buy_config.get('near_low_threshold', 1.002)
        low_weight = buy_config.get('near_low_weight', 0.4)

        # 条件1：价格在日内相对低位
        if relative_position > max_relative_pos:
            return {'should_buy': False, 'strength': 0.0, 'reason': '价格位置过高'}

        # 条件2：MACD底背离信号
        if (latest['macd_dif'] > prev['macd_dif'] and
            latest['close'] <= prev['close']):
            should_buy = True
            strength += macd_weight
            reasons.append('MACD底背离')

        # 条件3：KDJ超卖反弹
        if latest['kdj_k'] < kdj_threshold and latest['kdj_k'] > prev['kdj_k']:
            should_buy = True
            strength += kdj_weight
            reasons.append('KDJ超卖反弹')

        # 条件4：价格接近近期低点且有反弹迹象
        recent_low = data['low'].tail(10).min()
        if latest['close'] <= recent_low * low_threshold and latest['close'] > latest['low']:
            should_buy = True
            strength += low_weight
            reasons.append('接近低点反弹')

        # 位置越低，强度越高
        strength += (max_relative_pos - relative_position) * 2

        return {
            'should_buy': should_buy,
            'strength': min(3.0, max(0.5, strength)),
            'reason': '; '.join(reasons) if reasons else '无明确原因'
        }

    def _detect_sell_signal(self, latest, prev, relative_position: float, data: pd.DataFrame) -> Dict:
        """检测卖出信号"""
        should_sell = False
        strength = 1.0
        reasons = []

        # 从配置获取卖出信号参数
        sell_config = self.config.sell_signals
        min_relative_pos = sell_config.get('min_relative_position', 0.4)
        macd_weight = sell_config.get('macd_divergence_weight', 0.5)
        kdj_threshold = sell_config.get('kdj_overbought_threshold', 80)
        kdj_weight = sell_config.get('kdj_pullback_weight', 0.3)
        high_threshold = sell_config.get('near_high_threshold', 0.998)
        high_weight = sell_config.get('near_high_weight', 0.4)

        # 条件1：价格在日内相对高位
        if relative_position < min_relative_pos:
            return {'should_sell': False, 'strength': 0.0, 'reason': '价格位置过低'}

        # 条件2：MACD顶背离信号
        if (latest['macd_dif'] < prev['macd_dif'] and
            latest['close'] >= prev['close']):
            should_sell = True
            strength += macd_weight
            reasons.append('MACD顶背离')

        # 条件3：KDJ超买回调
        if latest['kdj_k'] > kdj_threshold and latest['kdj_k'] < prev['kdj_k']:
            should_sell = True
            strength += kdj_weight
            reasons.append('KDJ超买回调')

        # 条件4：价格接近近期高点且有回调迹象
        recent_high = data['high'].tail(10).max()
        if latest['close'] >= recent_high * high_threshold and latest['close'] < latest['high']:
            should_sell = True
            strength += high_weight
            reasons.append('接近高点回调')

        # 位置越高，强度越高
        strength += (relative_position - min_relative_pos) * 2

        return {
            'should_sell': should_sell,
            'strength': min(3.0, max(0.5, strength)),
            'reason': '; '.join(reasons) if reasons else '无明确原因'
        }

    def _should_skip_trade(self, symbol: str, current_price: float) -> bool:
        """检查是否应该跳过交易（防重复交易）"""
        current_time = datetime.now()
        state = self.trade_state[symbol]

        # 检查交易间隔
        if state['last_buy_time'] and state['last_sell_time']:
            last_trade_time = max(state['last_buy_time'], state['last_sell_time'])
            if (current_time - last_trade_time).total_seconds() < self.config.min_trade_interval * 60:
                return True

        # 检查连续同方向交易
        if state['consecutive_same_direction'] >= self.config.max_consecutive_trades:
            return True

        # 检查价格变动
        if state['last_buy_price']:
            price_change = abs(current_price - state['last_buy_price']) / state['last_buy_price']
            if price_change < self.config.min_price_change:
                return True

        # 检查每日交易次数限制
        if self.daily_trades >= self.config.daily_max_trades:
            return True

        return False

class PositionManager:
    """仓位管理器 - 基于原T0T策略的多时间框架仓位管理"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.position_config = config.get("risk_management.position_management", {})

        # 仓位参数
        self.base_position_ratio = self.position_config.get("base_position_ratio", 0.5)
        self.min_position_ratio = self.position_config.get("min_position_ratio", 0.15)
        self.max_position_ratio = self.position_config.get("max_position_ratio", 0.85)

        # 调整限制
        self.monthly_max_increase = self.position_config.get("monthly_adjustment.max_increase", 0.1)
        self.monthly_max_decrease = self.position_config.get("monthly_adjustment.max_decrease", 0.1)
        self.weekly_max_increase = self.position_config.get("weekly_adjustment.max_increase", 0.06)
        self.weekly_max_decrease = self.position_config.get("weekly_adjustment.max_decrease", 0.06)
        self.daily_max_increase = self.position_config.get("daily_adjustment.max_increase", 0.04)
        self.daily_max_decrease = self.position_config.get("daily_adjustment.max_decrease", 0.04)

        # 当前仓位状态
        self.current_positions = {}  # 各股票当前仓位
        self.position_history = []   # 仓位历史记录

    def calculate_target_position(self, symbol: str, monthly_data: pd.DataFrame = None,
                                weekly_data: pd.DataFrame = None, daily_data: pd.DataFrame = None) -> float:
        """计算目标仓位"""
        try:
            # 如果没有足够的历史数据，使用基础仓位
            if (monthly_data is None or len(monthly_data) < 144 or
                weekly_data is None or len(weekly_data) < 20 or
                daily_data is None or len(daily_data) < 20):
                logger.info(f"{symbol} 数据不足，使用基础仓位: {self.base_position_ratio}")
                return self.base_position_ratio

            # 1. 计算月度目标仓位
            monthly_target = self._calculate_monthly_position(symbol, monthly_data)

            # 2. 基于周线调整
            weekly_target = self._calculate_weekly_adjustment(symbol, weekly_data, monthly_target)

            # 3. 基于日线微调
            daily_target = self._calculate_daily_adjustment(symbol, daily_data, weekly_target)

            # 4. 限制在允许范围内
            final_target = max(self.min_position_ratio,
                             min(self.max_position_ratio, daily_target))

            logger.info(f"{symbol} 仓位计算: 月度={monthly_target:.3f}, 周度={weekly_target:.3f}, "
                       f"日度={daily_target:.3f}, 最终={final_target:.3f}")

            return final_target

        except Exception as e:
            logger.error(f"计算 {symbol} 目标仓位失败: {e}")
            return self.base_position_ratio

    def _calculate_monthly_position(self, symbol: str, monthly_data: pd.DataFrame) -> float:
        """计算月度目标仓位"""
        current_data = monthly_data.iloc[-1]
        current_position = self.current_positions.get(symbol, self.base_position_ratio)

        # 检查144月均线突破
        if 'ma_144' in monthly_data.columns:
            ma144 = current_data['ma_144']
            close_price = current_data['close']

            # 跌破144月均线 - 加仓信号
            if close_price < ma144:
                # 检查是否首次跌破
                if len(monthly_data) > 1:
                    prev_data = monthly_data.iloc[-2]
                    if prev_data['close'] >= prev_data['ma_144']:
                        # 首次跌破，加仓至少50%
                        target = max(0.5, current_position + self.monthly_max_increase)
                        logger.info(f"{symbol} 首次跌破144月均线，加仓至 {target:.3f}")
                        return target

                # 持续跌破，继续加仓
                target = min(self.max_position_ratio, current_position + self.monthly_max_increase)
                return target

        # 检查5年高低点和背离
        if len(monthly_data) >= 60:  # 5年数据
            historical_high = monthly_data['high'].rolling(60).max().iloc[-1]
            historical_low = monthly_data['low'].rolling(60).min().iloc[-1]
            current_high = current_data['high']
            current_low = current_data['low']

            # 创5年新低且出现底背离 - 最大加仓
            if (current_low <= historical_low and
                'bullish_divergence' in current_data and current_data['bullish_divergence']):
                logger.info(f"{symbol} 创5年新低且底背离，加仓至最大仓位")
                return self.max_position_ratio

            # 创5年新高且出现顶背离 - 减仓
            if (current_high >= historical_high and
                'bearish_divergence' in current_data and current_data['bearish_divergence']):
                target = max(self.min_position_ratio, current_position - self.monthly_max_decrease)
                logger.info(f"{symbol} 创5年新高且顶背离，减仓至 {target:.3f}")
                return target

        return current_position

    def _calculate_weekly_adjustment(self, symbol: str, weekly_data: pd.DataFrame, monthly_target: float) -> float:
        """计算周度仓位调整"""
        current_data = weekly_data.iloc[-1]

        # 检查周线背离信号
        if 'bullish_divergence' in current_data and current_data['bullish_divergence']:
            # 底背离，在月度目标基础上加仓
            target = min(self.max_position_ratio, monthly_target + self.weekly_max_increase)
            logger.info(f"{symbol} 周线底背离，加仓至 {target:.3f}")
            return target

        if 'bearish_divergence' in current_data and current_data['bearish_divergence']:
            # 顶背离，在月度目标基础上减仓
            target = max(self.min_position_ratio, monthly_target - self.weekly_max_decrease)
            logger.info(f"{symbol} 周线顶背离，减仓至 {target:.3f}")
            return target

        return monthly_target

    def _calculate_daily_adjustment(self, symbol: str, daily_data: pd.DataFrame, weekly_target: float) -> float:
        """计算日度仓位微调"""
        current_data = daily_data.iloc[-1]

        # 检查日线背离信号
        if 'bullish_divergence' in current_data and current_data['bullish_divergence']:
            # 底背离，在周度目标基础上加仓
            target = min(self.max_position_ratio, weekly_target + self.daily_max_increase)
            logger.info(f"{symbol} 日线底背离，加仓至 {target:.3f}")
            return target

        if 'bearish_divergence' in current_data and current_data['bearish_divergence']:
            # 顶背离，在周度目标基础上减仓
            target = max(self.min_position_ratio, weekly_target - self.daily_max_decrease)
            logger.info(f"{symbol} 日线顶背离，减仓至 {target:.3f}")
            return target

        return weekly_target

    def update_position(self, symbol: str, new_position: float, reason: str = ""):
        """更新仓位"""
        old_position = self.current_positions.get(symbol, self.base_position_ratio)
        self.current_positions[symbol] = new_position

        # 记录仓位变化
        position_record = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'old_position': old_position,
            'new_position': new_position,
            'change': new_position - old_position,
            'reason': reason
        }
        self.position_history.append(position_record)

        # 发送通知
        if abs(new_position - old_position) > 0.01:  # 变化超过1%才通知
            change_pct = (new_position - old_position) * 100
            notice_msg = (f"仓位调整: {symbol} "
                         f"{old_position:.1%} → {new_position:.1%} "
                         f"({change_pct:+.1f}%) "
                         f"原因: {reason}")
            self._send_notice(notice_msg)
            logger.info(notice_msg)

    def get_current_position(self, symbol: str) -> float:
        """获取当前仓位"""
        return self.current_positions.get(symbol, self.base_position_ratio)

    def _send_notice(self, message: str):
        """发送通知"""
        try:
            # 使用正确的Smart API通知方法
            smart.smart.notice(message)
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            # 通知失败不影响系统运行

class RiskManager:
    """风险管理器"""

    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.daily_pnl = 0.0  # 当日盈亏
        self.max_drawdown = 0.0  # 最大回撤
        self.consecutive_losses = 0  # 连续亏损次数
        self.order_failures = 0  # 下单失败次数
        self.total_trades = 0  # 总交易次数
        self.winning_trades = 0  # 盈利交易次数

    def check_trading_allowed(self, symbol: str, action: str, price: float, volume: int) -> Dict:
        """检查是否允许交易"""

        # 检查每日最大交易次数
        if self.total_trades >= self.config.daily_max_trades:
            return {
                'allowed': False,
                'reason': f'已达到每日最大交易次数限制: {self.config.daily_max_trades}'
            }

        # 检查每日最大亏损
        max_daily_loss = config.get('monitoring.performance_alerts.max_daily_loss', 0.05)
        if self.daily_pnl < -max_daily_loss:
            return {
                'allowed': False,
                'reason': f'已达到每日最大亏损限制: {max_daily_loss*100:.1f}%'
            }

        # 检查连续亏损次数
        max_consecutive_losses = config.get('monitoring.performance_alerts.max_consecutive_losses', 5)
        if self.consecutive_losses >= max_consecutive_losses:
            return {
                'allowed': False,
                'reason': f'连续亏损次数过多: {self.consecutive_losses}'
            }

        # 检查下单失败次数
        max_order_failures = config.get('monitoring.system_alerts.max_order_failures', 10)
        if self.order_failures >= max_order_failures:
            return {
                'allowed': False,
                'reason': f'下单失败次数过多: {self.order_failures}'
            }

        # 检查交易时间
        if not self._is_trading_time():
            return {
                'allowed': False,
                'reason': '当前不在交易时间内'
            }

        return {'allowed': True, 'reason': '风险检查通过'}

    def _is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')

        trading_hours = config.get_trading_hours()
        morning_start = trading_hours.get('morning_start', '09:30')
        morning_end = trading_hours.get('morning_end', '11:30')
        afternoon_start = trading_hours.get('afternoon_start', '13:00')
        afternoon_end = trading_hours.get('afternoon_end', '15:00')

        # 检查是否在交易时间段内
        in_morning = morning_start <= current_time <= morning_end
        in_afternoon = afternoon_start <= current_time <= afternoon_end

        return in_morning or in_afternoon

    def should_force_close(self) -> bool:
        """检查是否应该强制平仓"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')
        force_close_time = config.get_force_close_time()

        return current_time >= force_close_time

    def record_trade_result(self, symbol: str, side: str, pnl: float):
        """记录交易结果"""
        self.total_trades += 1
        self.daily_pnl += pnl

        if pnl > 0:
            self.winning_trades += 1
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1

        # 更新最大回撤
        if self.daily_pnl < self.max_drawdown:
            self.max_drawdown = self.daily_pnl

        logger.info(f"交易记录: {symbol} {side} 盈亏:{pnl:.2f} 当日总盈亏:{self.daily_pnl:.2f}")

    def record_order_failure(self):
        """记录下单失败"""
        self.order_failures += 1
        logger.warning(f"下单失败次数: {self.order_failures}")

    def get_risk_metrics(self) -> Dict:
        """获取风险指标"""
        win_rate = self.winning_trades / max(1, self.total_trades)

        return {
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'consecutive_losses': self.consecutive_losses,
            'order_failures': self.order_failures
        }

    def reset_daily_stats(self):
        """重置每日统计"""
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.order_failures = 0
        self.total_trades = 0
        self.winning_trades = 0
        logger.info("每日风险统计已重置")

class TradeExecutor:
    """交易执行器"""

    def __init__(self, config: T0TradingConfig, risk_manager: RiskManager):
        self.config = config
        self.risk_manager = risk_manager
        self.pending_orders = {}  # 待处理订单
        self.executed_trades = []  # 已执行交易记录

    def execute_trade(self, symbol: str, action: str, price: float, strength: float, reason: str):
        """执行交易"""
        try:
            # 计算交易数量
            volume = self._calculate_trade_volume(symbol, strength)
            if volume <= 0:
                logger.warning(f"交易数量为0，跳过交易: {symbol} {action}")
                return False

            # 风险检查
            risk_check = self.risk_manager.check_trading_allowed(symbol, action, price, volume)
            if not risk_check['allowed']:
                logger.warning(f"风险检查未通过: {risk_check['reason']}")
                return False

            # 确定交易方向
            side = smart.Type.Side.Buy if action == 'buy' else smart.Type.Side.Sell

            # 下单前通知
            action_cn = "买入" if action == 'buy' else "卖出"
            pre_trade_msg = (f"准备{action_cn}: {symbol} "
                           f"{volume}股 @{price:.2f} "
                           f"强度:{strength:.1f} "
                           f"原因:{reason}")
            self._send_notice(pre_trade_msg)
            logger.info(pre_trade_msg)

            # 下单
            order_id = self._place_order(symbol, side, price, volume, reason)

            if order_id:
                # 交易成功通知
                success_msg = (f"✅ {action_cn}成功: {symbol} "
                             f"{volume}股 @{price:.2f} "
                             f"订单号:{order_id} "
                             f"原因:{reason}")
                self._send_notice(success_msg)
                logger.info(success_msg)
                return True
            else:
                # 交易失败通知
                fail_msg = f"❌ {action_cn}失败: {symbol} {volume}股 @{price:.2f}"
                self._send_notice(fail_msg)
                logger.error(fail_msg)
                self.risk_manager.record_order_failure()
                return False

        except Exception as e:
            error_msg = f"❌ 交易执行异常: {symbol} {action} - {str(e)}"
            self._send_notice(error_msg)
            logger.error(error_msg)
            self.risk_manager.record_order_failure()
            return False

    def _calculate_trade_volume(self, symbol: str, strength: float) -> int:
        """计算交易数量"""
        # 这里需要根据实际持仓和资金情况计算
        # 暂时使用固定数量，实际应用中需要动态计算
        base_volume = 100  # 基础交易量（手）
        volume = int(base_volume * strength)

        # 确保是100的整数倍（A股最小交易单位）
        volume = (volume // 100) * 100

        return max(100, volume)  # 最小100股

    def _place_order(self, symbol: str, side, price: float, volume: int, reason: str) -> Optional[str]:
        """下单"""
        try:
            order_id = f"T0_{symbol}_{datetime.now().strftime('%H%M%S')}"

            def order_callback(order, err):
                if err:
                    logger.error(f"下单失败: {err}")
                else:
                    logger.info(f"下单成功: {smart.utils.toString(order)}")
                    self.pending_orders[order_id] = {
                        'symbol': symbol,
                        'side': side,
                        'price': price,
                        'volume': volume,
                        'reason': reason,
                        'timestamp': datetime.now(),
                        'order': order
                    }

            # 使用Smart API下单
            smart.insert_order(
                code=symbol,
                limit_price=price,
                volume=volume,
                side=side,
                callback=order_callback
            )

            return order_id

        except Exception as e:
            logger.error(f"下单时发生错误: {e}")
            return None



    def on_order_update(self, order):
        """订单状态更新回调"""
        try:
            logger.info(f"订单更新: {smart.utils.toString(order)}")
            # 这里可以添加订单状态处理逻辑
        except Exception as e:
            logger.error(f"处理订单更新时发生错误: {e}")

    def on_trade_update(self, trade):
        """成交回调"""
        try:
            logger.info(f"成交通知: {smart.utils.toString(trade)}")

            # 记录成交信息
            trade_record = {
                'symbol': trade.instrument_id,
                'side': 'buy' if trade.side == smart.Type.Side.Buy else 'sell',
                'price': trade.price,
                'volume': trade.volume,
                'timestamp': datetime.now(),
                'trade_id': trade.trade_id
            }

            self.executed_trades.append(trade_record)

        except Exception as e:
            logger.error(f"处理成交通知时发生错误: {e}")

class T0TradingSystem:
    """T0交易系统主控制器"""

    def __init__(self):
        self.config = T0TradingConfig()
        self.data_manager = MarketDataManager(self.config)
        self.strategy = T0TradingStrategy(self.config)
        self.position_manager = PositionManager(self.config)
        self.risk_manager = RiskManager(self.config)
        self.executor = TradeExecutor(self.config, self.risk_manager)
        self.is_running = False

        # 设置数据回调
        self.data_manager.add_data_callback(self.on_market_data_update)

    def start(self):
        """启动交易系统"""
        try:
            logger.info("启动T0交易系统...")

            # 发送启动通知
            start_msg = f"🚀 T0交易系统启动中... 目标股票: {', '.join(self.config.target_symbols)}"
            self._send_notice(start_msg)

            # 1. 加载warm-up数据
            logger.info("开始加载warm-up数据...")
            self.data_manager.load_warmup_data(self.config.target_symbols)

            # 2. 订阅行情数据
            self.data_manager.subscribe_quotes(self.config.target_symbols)

            # 3. 设置分钟级定时器
            self.data_manager.setup_minute_timer()

            # 4. 设置Smart API回调
            try:
                # 设置行情回调
                smart.smart.on('quote', self.data_manager.on_quote_received)
                # 设置订单和成交回调（如果有账户对象）
                if hasattr(smart.smart, 'current_account') and smart.smart.current_account:
                    smart.smart.current_account.on_order = self.executor.on_order_update
                    smart.smart.current_account.on_trade = self.executor.on_trade_update
                logger.info("Smart API回调设置完成")
            except Exception as e:
                logger.warning(f"设置Smart API回调时出现问题: {e}")
                # 回调设置失败不影响系统启动

            self.is_running = True

            # 发送启动成功通知
            success_msg = "✅ T0交易系统启动成功！"
            self._send_notice(success_msg)
            logger.info(success_msg)

        except Exception as e:
            error_msg = f"❌ 启动T0交易系统失败: {e}"
            self._send_notice(error_msg)
            logger.error(error_msg)
            self.is_running = False

    def stop(self):
        """停止交易系统"""
        self.is_running = False
        logger.info("T0交易系统已停止")

    def on_market_data_update(self, symbol: str, quote_data: Dict):
        """市场数据更新回调"""
        if not self.is_running:
            return

        try:
            # 检查是否需要强制平仓
            if self.risk_manager.should_force_close():
                self._force_close_positions()
                return

            # 检查股票是否已完成warm-up
            if not self.data_manager.is_symbol_ready_for_trading(symbol):
                logger.debug(f"{symbol} 尚未完成warm-up，跳过交易信号检测")
                return

            # 检查是否在交易时间内
            if not self.data_manager._is_trading_time():
                return

            # 获取分钟级数据
            minute_data = self.data_manager.get_minute_data(symbol, 100)

            if len(minute_data) < 30:  # 数据不足，跳过
                return

            # 分析市场数据，生成交易信号
            signal = self.strategy.analyze_market_data(symbol, minute_data)

            # 执行交易
            if signal['action'] in ['buy', 'sell']:
                success = self.executor.execute_trade(
                    symbol=symbol,
                    action=signal['action'],
                    price=signal['price'],
                    strength=signal['strength'],
                    reason=signal['reason']
                )

                if success:
                    # 更新交易状态
                    self._update_trade_state(symbol, signal)

        except Exception as e:
            logger.error(f"处理市场数据更新时发生错误: {e}")

    def _force_close_positions(self):
        """强制平仓"""
        logger.warning("触发强制平仓时间，开始平仓操作...")

        # 这里应该获取当前持仓并强制平仓
        # 由于需要Smart API的持仓信息，这里只是示例逻辑
        for symbol in self.config.target_symbols:
            try:
                # 获取当前价格
                if symbol in self.data_manager.quote_data:
                    current_price = self.data_manager.quote_data[symbol]['last_price']

                    # 强制卖出（这里需要根据实际持仓情况调整）
                    self.executor.execute_trade(
                        symbol=symbol,
                        action='sell',
                        price=current_price,
                        strength=3.0,  # 最高强度
                        reason='强制平仓'
                    )

            except Exception as e:
                logger.error(f"强制平仓失败 {symbol}: {e}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        risk_metrics = self.risk_manager.get_risk_metrics()

        return {
            'is_running': self.is_running,
            'target_symbols': self.config.target_symbols,
            'risk_metrics': risk_metrics,
            'data_status': {
                'subscribed_symbols': list(self.data_manager.quote_data.keys()),
                'minute_data_count': {
                    symbol: len(data) for symbol, data in self.data_manager.minute_data.items()
                }
            }
        }



    def _update_trade_state(self, symbol: str, signal: Dict):
        """更新交易状态"""
        state = self.strategy.trade_state[symbol]
        current_time = datetime.now()

        if signal['action'] == 'buy':
            state['last_buy_price'] = signal['price']
            state['last_buy_time'] = current_time

            if state['last_trade_direction'] == 'buy':
                state['consecutive_same_direction'] += 1
            else:
                state['consecutive_same_direction'] = 1
            state['last_trade_direction'] = 'buy'

        elif signal['action'] == 'sell':
            state['last_sell_price'] = signal['price']
            state['last_sell_time'] = current_time

            if state['last_trade_direction'] == 'sell':
                state['consecutive_same_direction'] += 1
            else:
                state['consecutive_same_direction'] = 1
            state['last_trade_direction'] = 'sell'

        # 增加当日交易次数
        self.strategy.daily_trades += 1

# 全局交易系统实例
trading_system = T0TradingSystem()

def init():
    """Smart平台初始化回调"""
    logger.info("Smart平台初始化...")

    # 启动T0交易系统
    trading_system.start()

def show():
    """显示回调"""
    logger.info("显示T0交易系统界面")

def hide():
    """隐藏回调"""
    logger.info("隐藏T0交易系统界面")

def close():
    """关闭回调"""
    logger.info("关闭T0交易系统")
    trading_system.stop()

# 注册Smart平台回调
smart.on_init(init)
smart.on_show(show)
smart.on_hide(hide)
smart.on_close(close)