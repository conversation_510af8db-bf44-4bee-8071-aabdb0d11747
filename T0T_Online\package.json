{"name": "T0T_Online", "version": "1.0.0", "minSmartSdkVersion": "1.3.0", "description": "T0T Online", "author": "freefolk", "menuText": "T0T Online", "plugin_type": "PLUGIN_TYPE_PYTHON", "plugin_domain": "PLUGIN_DOMAIN_PROGRAM_TRADEL", "keywords": ["T0T"], "isPublic": false, "isOpensource": false, "project_dir": "C:\\Users\\<USER>\\.xtp-smart\\plugins\\T0T_Online", "project_template": "plugin_python", "project_template_version": "2.0", "homepage": "", "main": "index.js", "scripts": {"build": "node build.js"}, "rely_items": {}, "dependencies": {"@xtp-smart/style": "^0.9.1", "@xtp-smart/ui": "^0.9.9", "iview": "2.13.0", "numeral": "^2.0.6", "throttle-debounce": "^2.1.0", "vue": "^2.5.17", "vue-router": "^3.0.1", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@vue/test-utils": "^1.0.0", "ajv": "^6.5.0", "babel-loader": "^8.1.0", "cfonts": "^2.1.2", "chai": "^4.1.2", "chalk": "^2.4.1", "config": "^1.30.0", "config-webpack": "^1.0.4", "copy-webpack-plugin": "^4.5.1", "cross-env": "^5.1.6", "css-loader": "^2.1.0", "del": "^3.0.0", "file-loader": "^4.0.0", "html-webpack-plugin": "^3.2.0", "markdown-it-plantuml": "^1.4.1", "mini-css-extract-plugin": "^0.4.0", "multispinner": "^0.2.1", "node-loader": "^0.6.0", "node-sass": "^4.9.2", "require-dir": "^1.0.0", "sass-loader": "^7.0.3", "style-loader": "^1.1.0", "ts-loader": "^8.0.6", "typescript": "^4.0.0", "url-loader": "^2.0.0", "vue-html-loader": "^1.2.4", "vue-loader": "^15.2.4", "vue-style-loader": "^4.1.0", "vue-template-compiler": "^2.5.16", "webpack": "^4.15.1", "webpack-cli": "^4.1.0", "webpack-dev-server": "^3.1.4", "webpack-hot-middleware": "^2.22.2", "webpack-merge": "^4.1.3", "fs-extra": "^9.0.0"}, "changeLog": ""}