#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Smart API的可用方法和属性
"""

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import smart
    logger.info("Smart API 导入成功")
    
    # 检查smart模块的所有属性和方法
    print("=" * 50)
    print("Smart API 可用属性和方法:")
    print("=" * 50)
    
    smart_attrs = [attr for attr in dir(smart) if not attr.startswith('_')]
    for attr in sorted(smart_attrs):
        attr_obj = getattr(smart, attr)
        attr_type = type(attr_obj).__name__
        print(f"{attr}: {attr_type}")
        
        # 如果是类，也显示其方法
        if hasattr(attr_obj, '__dict__') and attr_type == 'type':
            methods = [m for m in dir(attr_obj) if not m.startswith('_') and callable(getattr(attr_obj, m))]
            if methods:
                print(f"  -> 方法: {', '.join(methods[:5])}{'...' if len(methods) > 5 else ''}")
    
    print("\n" + "=" * 50)
    print("检查常用功能:")
    print("=" * 50)
    
    # 检查通知功能
    notice_methods = [attr for attr in smart_attrs if 'notice' in attr.lower()]
    print(f"通知相关方法: {notice_methods}")
    
    # 检查数据查询功能
    query_methods = [attr for attr in smart_attrs if 'query' in attr.lower()]
    print(f"查询相关方法: {query_methods}")
    
    # 检查订阅功能
    subscribe_methods = [attr for attr in smart_attrs if 'subscribe' in attr.lower() or 'sub' in attr.lower()]
    print(f"订阅相关方法: {subscribe_methods}")
    
    # 检查定时器功能
    timer_methods = [attr for attr in smart_attrs if 'time' in attr.lower() or 'timer' in attr.lower()]
    print(f"定时器相关方法: {timer_methods}")
    
    # 检查账户相关
    account_methods = [attr for attr in smart_attrs if 'account' in attr.lower()]
    print(f"账户相关方法: {account_methods}")
    
    # 检查事件相关
    event_methods = [attr for attr in smart_attrs if 'event' in attr.lower() or 'on' in attr.lower()]
    print(f"事件相关方法: {event_methods}")
    
    print("\n" + "=" * 50)
    print("尝试调用一些方法:")
    print("=" * 50)
    
    # 尝试获取版本信息
    if hasattr(smart, 'version'):
        try:
            print(f"Smart API 版本: {smart.version}")
        except Exception as e:
            print(f"获取版本失败: {e}")
    
    # 尝试获取账户信息
    if hasattr(smart, 'current_account'):
        try:
            print(f"当前账户: {smart.current_account}")
        except Exception as e:
            print(f"获取账户失败: {e}")
    
    # 检查是否有配置信息
    if hasattr(smart, 'config'):
        try:
            print(f"Smart配置: {smart.config}")
        except Exception as e:
            print(f"获取配置失败: {e}")
            
except ImportError as e:
    logger.error(f"无法导入Smart API: {e}")
except Exception as e:
    logger.error(f"检查Smart API时发生错误: {e}")
