"""
配置加载器 - 从YAML文件加载T0交易系统配置
"""

import yaml
import os
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                self.config = self._get_default_config()
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            
            logger.info(f"成功加载配置文件: {self.config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config = self._get_default_config()
    
    def get(self, key_path: str, default=None):
        """
        获取配置值，支持点号分隔的路径
        例如: get("trading.min_trade_portion")
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        return self.get("trading", {})
    
    def get_indicators_config(self) -> Dict[str, Any]:
        """获取技术指标配置"""
        return self.get("indicators", {})
    
    def get_risk_management_config(self) -> Dict[str, Any]:
        """获取风险管理配置"""
        return self.get("risk_management", {})
    
    def get_signal_detection_config(self) -> Dict[str, Any]:
        """获取信号检测配置"""
        return self.get("signal_detection", {})
    
    def get_target_symbols(self) -> List[str]:
        """获取交易标的列表"""
        return self.get("trading.target_symbols", ["600036.SH"])
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get("logging", {})
    
    def is_live_trading_enabled(self) -> bool:
        """是否启用实盘交易"""
        return self.get("live_trading.enabled", True)
    
    def is_paper_trading(self) -> bool:
        """是否为模拟交易"""
        return self.get("live_trading.paper_trading", False)
    
    def get_trading_hours(self) -> Dict[str, str]:
        """获取交易时间配置"""
        return self.get("live_trading.trading_hours", {
            "morning_start": "09:30",
            "morning_end": "11:30", 
            "afternoon_start": "13:00",
            "afternoon_end": "15:00"
        })
    
    def get_force_close_time(self) -> str:
        """获取强制平仓时间"""
        return self.get("live_trading.force_close_time", "14:50")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "trading": {
                "target_symbols": ["600036.SH"],
                "min_trade_portion": 0.125,
                "max_trade_portion": 0.333,
                "price_threshold": 0.01,
                "transaction_cost_rate": 0.0014,
                "min_trade_interval": 3,
                "max_consecutive_trades": 2,
                "min_price_change": 0.002
            },
            "indicators": {
                "macd": {
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                },
                "kdj": {
                    "period": 9
                }
            },
            "risk_management": {
                "max_position_ratio": 0.8,
                "stop_loss_ratio": 0.05,
                "daily_max_trades": 20,
                "max_drawdown": 0.1
            },
            "signal_detection": {
                "buy_signals": {
                    "max_relative_position": 0.6,
                    "macd_divergence_weight": 0.5,
                    "kdj_oversold_threshold": 20,
                    "kdj_rebound_weight": 0.3,
                    "near_low_threshold": 1.002,
                    "near_low_weight": 0.4
                },
                "sell_signals": {
                    "min_relative_position": 0.4,
                    "macd_divergence_weight": 0.5,
                    "kdj_overbought_threshold": 80,
                    "kdj_pullback_weight": 0.3,
                    "near_high_threshold": 0.998,
                    "near_high_weight": 0.4
                }
            },
            "data_management": {
                "max_minute_bars": 1000,
                "indicator_warmup_periods": 30,
                "data_update_interval": 1
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "file_path": "logs/t0_trading.log",
                "console_enabled": True
            },
            "live_trading": {
                "enabled": True,
                "paper_trading": False,
                "trading_hours": {
                    "morning_start": "09:30",
                    "morning_end": "11:30",
                    "afternoon_start": "13:00", 
                    "afternoon_end": "15:00"
                },
                "force_close_time": "14:50"
            },
            "monitoring": {
                "enabled": True,
                "performance_alerts": {
                    "max_daily_loss": 0.05,
                    "max_consecutive_losses": 5
                },
                "system_alerts": {
                    "max_order_failures": 10,
                    "data_delay_threshold": 30
                }
            }
        }

# 全局配置实例
config = ConfigLoader()
