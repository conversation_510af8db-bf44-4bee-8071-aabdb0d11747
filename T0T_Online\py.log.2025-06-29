2025-06-29 21:34:51,067 - DEBUG - 12000 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-29 21:35:22,484 - DEBUG - 30232 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-29 21:35:22,497 - ERROR - 30232 - start.py - _send_notice - 767 - 发送通知失败: 'str' object has no attribute 'get'
2025-06-29 21:35:22,498 - INFO - 30232 - start.py - update_position - 756 - 仓位调整: 600036.SH 50.0% → 60.0% (+10.0%) 原因: 测试仓位调整
