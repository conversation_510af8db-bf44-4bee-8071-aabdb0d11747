# T0T_Online 交易系统配置文件

# 交易标的配置
trading:
  target_symbols:
    - "600036.SH"  # 招商银行
    - "600000.SH"  # 浦发银行
    # - "600519.SH"  # 贵州茅台
  
  # T0交易参数
  min_trade_portion: 0.125      # 最小交易比例 (1/8)
  max_trade_portion: 0.333      # 最大交易比例 (1/3)
  price_threshold: 0.01         # 价格变动阈值 1%
  transaction_cost_rate: 0.0014 # 交易成本率 0.14%
  
  # 防重复交易参数
  min_trade_interval: 3         # 最小交易间隔(分钟)
  max_consecutive_trades: 2     # 最大连续同方向交易次数
  min_price_change: 0.002       # 最小价格变动要求 0.2%

# 技术指标参数
indicators:
  macd:
    fast_period: 12
    slow_period: 26
    signal_period: 9
  
  kdj:
    period: 9
    k_smooth: 3
    d_smooth: 3

# 风险管理参数
risk_management:
  max_position_ratio: 0.8       # 最大仓位比例
  stop_loss_ratio: 0.05         # 止损比例 5%
  daily_max_trades: 20          # 每日最大交易次数
  max_drawdown: 0.1             # 最大回撤 10%
  
  # 仓位管理（基于原T0策略文档）
  position_management:
    base_position_ratio: 0.5    # 基础仓位比例
    min_position_ratio: 0.15    # 最小仓位比例
    max_position_ratio: 0.85    # 最大仓位比例
    
    # 月线仓位调整参数
    monthly_adjustment:
      max_increase: 0.1         # 月度最大加仓比例
      max_decrease: 0.1         # 月度最大减仓比例
    
    # 周线仓位调整参数  
    weekly_adjustment:
      max_increase: 0.06        # 周度最大加仓比例
      max_decrease: 0.06        # 周度最大减仓比例
    
    # 日线仓位调整参数
    daily_adjustment:
      max_increase: 0.04        # 日度最大加仓比例
      max_decrease: 0.04        # 日度最大减仓比例

# 信号检测参数
signal_detection:
  # 买入信号参数
  buy_signals:
    max_relative_position: 0.6  # 最大相对位置（超过则不买入）
    macd_divergence_weight: 0.5 # MACD背离权重
    kdj_oversold_threshold: 20  # KDJ超卖阈值
    kdj_rebound_weight: 0.3     # KDJ反弹权重
    near_low_threshold: 1.002   # 接近低点阈值
    near_low_weight: 0.4        # 接近低点权重
  
  # 卖出信号参数
  sell_signals:
    min_relative_position: 0.4  # 最小相对位置（低于则不卖出）
    macd_divergence_weight: 0.5 # MACD背离权重
    kdj_overbought_threshold: 80 # KDJ超买阈值
    kdj_pullback_weight: 0.3    # KDJ回调权重
    near_high_threshold: 0.998  # 接近高点阈值
    near_high_weight: 0.4       # 接近高点权重

# 数据管理参数
data_management:
  max_minute_bars: 1000         # 最大保存分钟K线数量
  indicator_warmup_periods: 30  # 指标预热周期
  data_update_interval: 1       # 数据更新间隔(秒)

# 日志配置
logging:
  level: "INFO"                 # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true            # 是否启用文件日志
  file_path: "logs/t0_trading.log"
  console_enabled: true         # 是否启用控制台日志

# 回测配置（如果需要）
backtest:
  enabled: false                # 是否启用回测模式
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  initial_capital: 1000000      # 初始资金
  
# 实盘交易配置
live_trading:
  enabled: true                 # 是否启用实盘交易
  paper_trading: false          # 是否为模拟交易
  
  # 交易时间限制
  trading_hours:
    morning_start: "09:30"
    morning_end: "11:30"
    afternoon_start: "13:00"
    afternoon_end: "15:00"
  
  # 强制平仓时间
  force_close_time: "14:50"     # 强制平仓时间
  
# 监控和报警配置
monitoring:
  enabled: true
  
  # 性能监控
  performance_alerts:
    max_daily_loss: 0.05        # 日最大亏损比例
    max_consecutive_losses: 5   # 最大连续亏损次数
  
  # 系统监控
  system_alerts:
    max_order_failures: 10      # 最大下单失败次数
    data_delay_threshold: 30    # 数据延迟阈值(秒)
