#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Smart API调用
"""

import logging
from datetime import datetime, timedelta
from config_loader import config

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger = logging.getLogger(__name__)

try:
    import smart
    logger.info("Smart API 导入成功")
except ImportError as e:
    logger.error(f"无法导入Smart API: {e}")
    exit(1)

class TradingDayCalculator:
    """交易日计算工具"""
    
    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（简化版本，排除周末）"""
        return date.weekday() < 5
    
    @staticmethod
    def get_previous_trading_days(count: int, from_date: datetime = None) -> list:
        """获取前N个交易日"""
        if from_date is None:
            from_date = datetime.now()
        
        trading_days = []
        current_date = from_date - timedelta(days=1)
        
        while len(trading_days) < count:
            if TradingDayCalculator.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date -= timedelta(days=1)
        
        return trading_days

def test_fixed_smart_api():
    """测试修复后的Smart API调用"""
    logger.info("🧪 开始测试修复后的Smart API...")
    
    # 1. 测试通知功能
    try:
        smart.smart.notice("🧪 测试修复后的通知功能")
        logger.info("✅ 通知功能测试成功")
    except Exception as e:
        logger.error(f"❌ 通知功能测试失败: {e}")
    
    # 2. 测试交易日计算
    try:
        trading_days = TradingDayCalculator.get_previous_trading_days(2)
        if trading_days:
            start_date = trading_days[-1]
            end_date = trading_days[0]
            logger.info(f"✅ 前两个交易日: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        else:
            logger.warning("⚠️ 未找到交易日")
    except Exception as e:
        logger.error(f"❌ 交易日计算失败: {e}")
    
    # 3. 测试数据查询
    try:
        symbols = config.get_target_symbols()
        if symbols:
            symbol = symbols[0]
            logger.info(f"测试查询 {symbol} 的数据...")
            
            # 使用修复后的API调用
            data = smart.smart.query_data_async(
                symbol,
                start_date.strftime('%Y%m%d'),
                end_date.strftime('%Y%m%d'),
                '1m'
            )
            logger.info(f"✅ query_data_async 调用成功，返回数据类型: {type(data)}")
            if hasattr(data, '__len__'):
                logger.info(f"数据长度: {len(data)}")
        else:
            logger.warning("⚠️ 未配置交易标的")
    except Exception as e:
        logger.error(f"❌ 数据查询测试失败: {e}")
    
    # 4. 测试订阅功能
    try:
        symbols = config.get_target_symbols()
        if symbols:
            smart.smart.subscribe(symbols)
            logger.info(f"✅ 订阅功能测试成功: {symbols}")
        else:
            logger.warning("⚠️ 未配置交易标的")
    except Exception as e:
        logger.error(f"❌ 订阅功能测试失败: {e}")
    
    # 5. 测试定时器功能
    try:
        def test_timer_callback():
            logger.info("⏰ 定时器回调测试")
        
        smart.smart.add_time_interval(5, test_timer_callback)  # 5秒测试
        logger.info("✅ 定时器设置成功")
    except Exception as e:
        logger.error(f"❌ 定时器设置失败: {e}")
    
    # 6. 测试事件回调设置
    try:
        def test_quote_callback(symbol, quote_data):
            logger.info(f"📈 收到行情回调: {symbol}")
        
        smart.smart.on('quote', test_quote_callback)
        logger.info("✅ 事件回调设置成功")
    except Exception as e:
        logger.error(f"❌ 事件回调设置失败: {e}")
    
    # 7. 测试交易时间检查
    try:
        now = datetime.now()
        current_time = now.strftime('%H:%M')
        
        is_morning = '09:30' <= current_time <= '11:30'
        is_afternoon = '13:00' <= current_time <= '15:00'
        is_trading_time = is_morning or is_afternoon
        
        logger.info(f"当前时间: {current_time}")
        logger.info(f"是否为交易时间: {is_trading_time}")
        
        if is_trading_time:
            logger.info("✅ 当前为交易时间")
        else:
            logger.info("⏰ 当前为非交易时间")
    except Exception as e:
        logger.error(f"❌ 交易时间检查失败: {e}")
    
    logger.info("🎯 Smart API测试完成")

def main():
    """主函数"""
    try:
        test_fixed_smart_api()
        
        # 发送完成通知
        try:
            smart.smart.notice("✅ Smart API修复测试完成！")
        except Exception as e:
            logger.warning(f"完成通知发送失败: {e}")
        
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        try:
            smart.smart.notice(f"❌ Smart API测试失败: {e}")
        except:
            pass

if __name__ == "__main__":
    main()
