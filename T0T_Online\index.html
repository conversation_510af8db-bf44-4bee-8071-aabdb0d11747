<!DOCTYPE html><html><head><meta charset=utf-8><meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name=viewport><style>/* CSS */

        html,
        body {
            overflow: hidden;
            color: #fff;
            font-size: 30px;
        }

        html,
        body,
        body>div:first-child {
            height: 100%;
            max-height: 100%;
            max-width: 100%;
            background-color: #282d38;
        }

        /* body>:not(:first-child) {
      display: none;
    } */

        body {
            background-color: #282d38;
            /* color: #b8c7ce; */
        }

        .wrapper {
            display: flex;
            justify-content: center;
            flex-direction: column;
        }

        .loading {
            margin: auto;
        }</style></head><body class=skin-dark><div id=app class=wrapper style="display: flex; justify-content: center; flex-direction: column;"></div><script type=text/javascript src=index.js></script></body></html>