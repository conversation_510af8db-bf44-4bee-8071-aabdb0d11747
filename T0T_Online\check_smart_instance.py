#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Smart实例的可用方法
"""

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import smart
    logger.info("Smart API 导入成功")
    
    # 检查smart实例的方法
    print("=" * 50)
    print("Smart 实例可用方法:")
    print("=" * 50)
    
    smart_instance_attrs = [attr for attr in dir(smart.smart) if not attr.startswith('_')]
    for attr in sorted(smart_instance_attrs):
        try:
            attr_obj = getattr(smart.smart, attr)
            attr_type = type(attr_obj).__name__
            print(f"{attr}: {attr_type}")
        except Exception as e:
            print(f"{attr}: 获取失败 - {e}")
    
    print("\n" + "=" * 50)
    print("检查具体功能:")
    print("=" * 50)
    
    # 检查通知功能
    if hasattr(smart.smart, 'notice'):
        print("✅ 找到 notice 方法")
        try:
            # 测试通知
            smart.smart.notice("🧪 测试通知")
            print("✅ 通知发送成功")
        except Exception as e:
            print(f"❌ 通知发送失败: {e}")
    else:
        print("❌ 未找到 notice 方法")
        # 查找类似的方法
        notice_like = [attr for attr in smart_instance_attrs if 'notice' in attr.lower() or 'msg' in attr.lower() or 'send' in attr.lower()]
        print(f"类似方法: {notice_like}")
    
    # 检查数据查询
    query_methods = [attr for attr in smart_instance_attrs if 'query' in attr.lower() or 'get' in attr.lower() or 'data' in attr.lower()]
    print(f"数据查询相关方法: {query_methods}")
    
    # 检查订阅功能
    subscribe_methods = [attr for attr in smart_instance_attrs if 'subscribe' in attr.lower() or 'sub' in attr.lower()]
    print(f"订阅相关方法: {subscribe_methods}")
    
    # 检查定时器功能
    timer_methods = [attr for attr in smart_instance_attrs if 'timer' in attr.lower() or 'time' in attr.lower() or 'interval' in attr.lower()]
    print(f"定时器相关方法: {timer_methods}")
    
    # 检查账户
    if hasattr(smart.smart, 'current_account'):
        print(f"✅ 找到 current_account: {smart.smart.current_account}")
    else:
        print("❌ 未找到 current_account")
        account_methods = [attr for attr in smart_instance_attrs if 'account' in attr.lower()]
        print(f"账户相关方法: {account_methods}")
    
    # 检查事件系统
    event_methods = [attr for attr in smart_instance_attrs if 'on' in attr.lower() or 'event' in attr.lower() or 'callback' in attr.lower()]
    print(f"事件相关方法: {event_methods}")
    
    print("\n" + "=" * 50)
    print("测试一些常用方法:")
    print("=" * 50)
    
    # 测试add_time_interval
    if hasattr(smart.smart, 'add_time_interval'):
        print("✅ 找到 add_time_interval 方法")
        try:
            def test_callback():
                print("⏰ 定时器回调测试")
            
            # 尝试不同的参数格式
            smart.smart.add_time_interval(5, test_callback)  # 5秒测试
            print("✅ add_time_interval 调用成功")
        except Exception as e:
            print(f"❌ add_time_interval 调用失败: {e}")
    else:
        print("❌ 未找到 add_time_interval 方法")
    
    # 测试subscribe
    if hasattr(smart.smart, 'subscribe'):
        print("✅ 找到 subscribe 方法")
        try:
            smart.smart.subscribe(['600036.SH'])
            print("✅ subscribe 调用成功")
        except Exception as e:
            print(f"❌ subscribe 调用失败: {e}")
    else:
        print("❌ 未找到 subscribe 方法")
        
    # 检查是否有其他数据获取方法
    if hasattr(smart.smart, 'query_market_data_async'):
        print("✅ 找到 query_market_data_async 方法")
    elif hasattr(smart.smart, 'query_data'):
        print("✅ 找到 query_data 方法")
    else:
        print("❌ 未找到常见的数据查询方法")
        
except ImportError as e:
    logger.error(f"无法导入Smart API: {e}")
except Exception as e:
    logger.error(f"检查Smart实例时发生错误: {e}")
