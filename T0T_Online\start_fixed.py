#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版T0交易系统启动脚本
解决了Smart API调用问题和交易日计算问题
"""

import logging
import sys
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Callable
from config_loader import config

# 配置日志
log_config = config.get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config.get('level', 'INFO')),
    format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger = logging.getLogger(__name__)

try:
    import smart
    logger.info("Smart API 导入成功")
except ImportError as e:
    logger.error(f"无法导入Smart API: {e}")
    sys.exit(1)

class TradingDayCalculator:
    """交易日计算工具"""
    
    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（简化版本，排除周末）"""
        return date.weekday() < 5
    
    @staticmethod
    def get_previous_trading_days(count: int, from_date: datetime = None) -> List[datetime]:
        """获取前N个交易日"""
        if from_date is None:
            from_date = datetime.now()
        
        trading_days = []
        current_date = from_date - timedelta(days=1)
        
        while len(trading_days) < count:
            if TradingDayCalculator.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date -= timedelta(days=1)
        
        return trading_days
    
    @staticmethod
    def get_trading_day_range(days_back: int, from_date: datetime = None) -> tuple:
        """获取交易日范围（开始日期，结束日期）"""
        if from_date is None:
            from_date = datetime.now()
        
        trading_days = TradingDayCalculator.get_previous_trading_days(days_back, from_date)
        if trading_days:
            start_date = trading_days[-1]
            end_date = trading_days[0]
            return start_date, end_date
        else:
            end_date = from_date - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back)
            return start_date, end_date

class T0TradingConfig:
    """T0交易配置"""
    def __init__(self):
        # 从配置文件加载参数
        trading_config = config.get_trading_config()
        indicators_config = config.get_indicators_config()
        risk_config = config.get_risk_management_config()
        signal_config = config.get_signal_detection_config()

        # 交易标的配置
        self.target_symbols = config.get_target_symbols()

        # T0交易参数
        self.min_trade_portion = trading_config.get('min_trade_portion', 1/8)
        self.max_trade_portion = trading_config.get('max_trade_portion', 1/3)
        self.price_threshold = trading_config.get('price_threshold', 0.01)
        self.transaction_cost_rate = trading_config.get('transaction_cost_rate', 0.0014)

        # 防重复交易参数
        self.min_trade_interval = trading_config.get('min_trade_interval', 3)
        self.max_consecutive_trades = trading_config.get('max_consecutive_trades', 2)
        self.min_price_change = trading_config.get('min_price_change', 0.002)

        # 技术指标参数
        macd_config = indicators_config.get('macd', {})
        self.macd_fast = macd_config.get('fast_period', 12)
        self.macd_slow = macd_config.get('slow_period', 26)
        self.macd_signal = macd_config.get('signal_period', 9)

        kdj_config = indicators_config.get('kdj', {})
        self.kdj_period = kdj_config.get('period', 9)

        # 风险管理参数
        self.max_position_ratio = risk_config.get('max_position_ratio', 0.8)
        self.stop_loss_ratio = risk_config.get('stop_loss_ratio', 0.05)
        self.daily_max_trades = risk_config.get('daily_max_trades', 20)

        # 信号检测参数
        self.buy_signals = signal_config.get('buy_signals', {})
        self.sell_signals = signal_config.get('sell_signals', {})

class SimpleMarketDataManager:
    """简化版市场数据管理器"""
    
    def __init__(self, config: T0TradingConfig):
        self.config = config
        self.quote_data = {}
        self.minute_data = {}
        self.is_warmed_up = {}
        self.warmup_required_periods = 50
        
        # 为每个股票初始化数据结构
        for symbol in self.config.target_symbols:
            self.minute_data[symbol] = deque(maxlen=1000)
            self.is_warmed_up[symbol] = False
    
    def load_warmup_data(self, symbols: List[str]):
        """加载warm-up数据（前两个交易日的分钟数据）"""
        try:
            for symbol in symbols:
                logger.info(f"开始加载 {symbol} 的warm-up数据...")
                
                try:
                    # 获取前两个交易日的日期范围
                    start_date, end_date = TradingDayCalculator.get_trading_day_range(2)
                    
                    logger.info(f"获取 {symbol} 从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的数据")
                    
                    # 使用Smart API获取历史数据
                    historical_data = smart.smart.query_data_async(
                        symbol,
                        start_date.strftime('%Y%m%d'),
                        end_date.strftime('%Y%m%d'),
                        '1m'
                    )
                    
                    if historical_data and len(historical_data) > 0:
                        # 处理历史数据
                        for data_point in historical_data:
                            self.minute_data[symbol].append(data_point)
                        
                        # 检查是否有足够的数据
                        if len(self.minute_data[symbol]) >= self.warmup_required_periods:
                            self.is_warmed_up[symbol] = True
                            logger.info(f"✅ {symbol} warm-up完成，数据条数: {len(self.minute_data[symbol])}")
                        else:
                            logger.warning(f"⚠️ {symbol} warm-up数据不足，当前: {len(self.minute_data[symbol])}, 需要: {self.warmup_required_periods}")
                    else:
                        logger.warning(f"⚠️ {symbol} 未获取到历史数据")
                        
                except Exception as e:
                    logger.error(f"获取 {symbol} 历史数据失败: {e}")
                    
        except Exception as e:
            logger.error(f"加载warm-up数据失败: {e}")
    
    def subscribe_quotes(self, symbols: List[str]):
        """订阅行情数据"""
        try:
            smart.smart.subscribe(symbols)
            logger.info(f"已订阅行情数据: {symbols}")
        except Exception as e:
            logger.error(f"订阅行情数据失败: {e}")
    
    def setup_minute_timer(self):
        """设置分钟级定时器"""
        try:
            def minute_timer_callback():
                logger.info("⏰ 分钟级定时器触发")
                # 这里可以添加分钟级数据处理逻辑
            
            smart.smart.add_time_interval(60, minute_timer_callback)
            logger.info("分钟级定时器已设置")
        except Exception as e:
            logger.error(f"设置分钟级定时器失败: {e}")
    
    def _is_trading_time(self) -> bool:
        """检查是否为交易时间"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')
        
        # 上午交易时间：9:30-11:30
        is_morning = '09:30' <= current_time <= '11:30'
        # 下午交易时间：13:00-15:00
        is_afternoon = '13:00' <= current_time <= '15:00'
        
        return is_morning or is_afternoon
    
    def is_symbol_ready_for_trading(self, symbol: str) -> bool:
        """检查股票是否准备好交易"""
        return (self.is_warmed_up.get(symbol, False) and 
                self._is_trading_time())

class FixedT0TradingSystem:
    """修复版T0交易系统"""
    
    def __init__(self):
        self.config = T0TradingConfig()
        self.data_manager = SimpleMarketDataManager(self.config)
        self.is_running = False
        logger.info("修复版T0交易系统初始化完成")
    
    def _send_notice_safe(self, message: str):
        """安全发送通知（避免配置错误）"""
        try:
            # 直接调用Smart API的notice方法，不依赖配置
            smart.smart.notice(message)
            logger.info(f"通知已发送: {message}")
        except Exception as e:
            logger.warning(f"通知发送失败（不影响系统运行）: {e}")
    
    def start(self):
        """启动交易系统"""
        try:
            logger.info("🚀 启动修复版T0交易系统...")
            
            # 发送启动通知（安全模式）
            self._send_notice_safe("🚀 修复版T0交易系统启动中...")
            
            # 1. 加载warm-up数据
            logger.info("开始加载warm-up数据...")
            self.data_manager.load_warmup_data(self.config.target_symbols)
            
            # 2. 订阅行情数据
            logger.info("订阅行情数据...")
            self.data_manager.subscribe_quotes(self.config.target_symbols)
            
            # 3. 设置分钟级定时器
            logger.info("设置分钟级定时器...")
            self.data_manager.setup_minute_timer()
            
            # 4. 设置事件回调
            try:
                def quote_callback(symbol, quote_data):
                    if self.data_manager.is_symbol_ready_for_trading(symbol):
                        logger.info(f"📈 收到 {symbol} 行情数据")
                
                smart.smart.on('quote', quote_callback)
                logger.info("事件回调设置完成")
            except Exception as e:
                logger.warning(f"设置事件回调失败: {e}")
            
            self.is_running = True
            
            # 发送启动成功通知
            success_msg = "✅ 修复版T0交易系统启动成功！"
            self._send_notice_safe(success_msg)
            logger.info(success_msg)
            
            # 显示系统状态
            self._show_system_status()
            
        except Exception as e:
            error_msg = f"❌ 启动修复版T0交易系统失败: {e}"
            self._send_notice_safe(error_msg)
            logger.error(error_msg)
            self.is_running = False
    
    def _show_system_status(self):
        """显示系统状态"""
        logger.info("=" * 50)
        logger.info("系统状态:")
        logger.info(f"运行状态: {'运行中' if self.is_running else '已停止'}")
        logger.info(f"交易标的: {self.config.target_symbols}")
        logger.info(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"是否为交易时间: {self.data_manager._is_trading_time()}")
        
        # 显示warm-up状态
        for symbol in self.config.target_symbols:
            warm_up_status = "✅" if self.data_manager.is_warmed_up.get(symbol, False) else "⏳"
            data_count = len(self.data_manager.minute_data.get(symbol, []))
            logger.info(f"{symbol}: {warm_up_status} 数据条数: {data_count}")
        
        logger.info("=" * 50)
    
    def stop(self):
        """停止交易系统"""
        self.is_running = False
        self._send_notice_safe("🛑 修复版T0交易系统已停止")
        logger.info("修复版T0交易系统已停止")

def main():
    """主函数"""
    try:
        # 创建并启动系统
        system = FixedT0TradingSystem()
        system.start()
        
        if system.is_running:
            logger.info("系统运行中，按 Ctrl+C 停止...")
            
            import time
            while system.is_running:
                time.sleep(1)
        
    except KeyboardInterrupt:
        logger.info("收到停止信号")
        if 'system' in locals():
            system.stop()
    except Exception as e:
        logger.error(f"系统运行时发生错误: {e}")

if __name__ == "__main__":
    main()
