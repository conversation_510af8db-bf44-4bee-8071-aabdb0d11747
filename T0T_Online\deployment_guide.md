# T0T_Online 部署指南

## 部署前准备

### 1. 环境检查
确保以下环境已准备就绪：
- Python 3.7+
- Smart交易平台已安装并配置
- 网络连接稳定
- 交易账户已开通并有足够资金

### 2. 依赖安装
```bash
pip install pandas pyyaml
```

### 3. 文件检查
确保以下文件存在：
- `start.py` - 主程序文件
- `config.yaml` - 配置文件
- `config_loader.py` - 配置加载器
- `test_system.py` - 测试脚本

## 配置步骤

### 1. 编辑配置文件
打开 `config.yaml`，根据需要修改以下配置：

#### 交易标的设置
```yaml
trading:
  target_symbols:
    - "600036.SH"  # 招商银行
    - "600000.SH"  # 浦发银行
```

#### 风险参数设置
```yaml
risk_management:
  max_position_ratio: 0.8      # 最大仓位比例
  stop_loss_ratio: 0.05        # 止损比例
  daily_max_trades: 20         # 每日最大交易次数
```

#### 技术指标参数
```yaml
indicators:
  macd:
    fast_period: 12
    slow_period: 26
    signal_period: 9
  kdj:
    period: 9
```

### 2. 测试系统
运行测试脚本验证系统功能：
```bash
python test_system.py
```

确保所有测试通过后再进行部署。

## 部署步骤

### 1. Smart平台集成
1. 打开Smart交易平台
2. 进入策略管理界面
3. 导入 `start.py` 文件
4. 确认配置文件路径正确

### 2. 启动系统
1. 在Smart平台中启动策略
2. 观察日志输出，确认系统正常启动
3. 检查行情数据订阅是否成功

### 3. 监控运行
启动后需要密切监控以下指标：
- 行情数据接收状态
- 交易信号生成情况
- 订单执行状态
- 风险指标变化

## 运行监控

### 1. 关键日志
注意观察以下日志信息：
```
INFO - 启动T0交易系统...
INFO - 已订阅行情数据: ['600036.SH']
INFO - T0交易系统启动成功
INFO - 交易执行成功: 600036.SH buy 100股 @10.50 原因:KDJ超卖反弹
```

### 2. 风险监控
定期检查风险指标：
- 当日盈亏
- 最大回撤
- 连续亏损次数
- 交易成功率

### 3. 异常处理
如遇到以下情况，需要及时处理：
- 连续下单失败
- 数据接收异常
- 超过风险限制

## 常见问题

### 1. 配置文件加载失败
**问题**: `No module named 'yaml'`
**解决**: 安装PyYAML: `pip install pyyaml`

### 2. Smart API连接失败
**问题**: 无法订阅行情数据
**解决**: 
- 检查Smart平台连接状态
- 确认交易账户权限
- 验证股票代码格式

### 3. 交易执行失败
**问题**: 下单失败
**解决**:
- 检查账户资金
- 确认交易时间
- 验证股票交易状态

### 4. 风险限制触发
**问题**: 达到风险限制无法交易
**解决**:
- 检查当日亏损情况
- 调整风险参数
- 重置风险统计

## 性能优化

### 1. 数据处理优化
- 限制历史数据保存量
- 优化技术指标计算
- 减少不必要的日志输出

### 2. 交易执行优化
- 合理设置交易间隔
- 优化信号检测逻辑
- 提高订单执行效率

### 3. 风险控制优化
- 动态调整风险参数
- 实时监控市场状况
- 及时响应异常情况

## 安全注意事项

### 1. 资金安全
- 设置合理的止损比例
- 控制单日最大亏损
- 定期检查账户状态

### 2. 系统安全
- 定期备份配置文件
- 监控系统运行状态
- 及时更新系统版本

### 3. 合规要求
- 遵守交易所规则
- 符合监管要求
- 保持交易记录

## 维护建议

### 1. 日常维护
- 每日检查系统运行状态
- 定期分析交易结果
- 及时调整策略参数

### 2. 定期维护
- 每周备份重要数据
- 每月评估策略效果
- 季度进行系统优化

### 3. 应急处理
- 制定应急预案
- 准备手动干预方案
- 建立快速响应机制

## 技术支持

如遇到技术问题，请：
1. 查看系统日志
2. 运行测试脚本
3. 检查配置文件
4. 联系技术支持

## 免责声明

本系统仅供学习和研究使用，实盘交易存在风险，请谨慎使用。使用者应当：
- 充分了解交易风险
- 合理控制资金规模
- 遵守相关法律法规
- 自行承担交易后果
