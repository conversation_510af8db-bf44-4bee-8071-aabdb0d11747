"""
T0T_Online 系统测试脚本
用于验证系统各个组件的功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_config_loader():
    """测试配置加载器"""
    try:
        from config_loader import config
        
        print("=== 配置加载器测试 ===")
        print(f"交易标的: {config.get_target_symbols()}")
        print(f"MACD参数: {config.get('indicators.macd')}")
        print(f"风险管理: {config.get_risk_management_config()}")
        print(f"实盘交易: {config.is_live_trading_enabled()}")
        print("配置加载器测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"配置加载器测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    try:
        # 导入需要的模块（注意：这里需要模拟smart模块）
        import sys
        sys.path.append('.')
        
        # 创建模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 10.0
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(0.1, new_price))  # 确保价格为正
        
        # 创建DataFrame
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        print("=== 技术指标测试 ===")
        print(f"原始数据形状: {data.shape}")
        print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        # 这里需要实际的TechnicalIndicators类来测试
        # 由于依赖smart模块，我们只能做基本验证
        print("技术指标测试准备完成 ✓")
        return True
        
    except Exception as e:
        print(f"技术指标测试失败: {e}")
        return False

def test_trading_config():
    """测试交易配置"""
    try:
        from config_loader import config
        
        # 模拟T0TradingConfig类的初始化
        class MockT0TradingConfig:
            def __init__(self):
                trading_config = config.get_trading_config()
                indicators_config = config.get_indicators_config()
                risk_config = config.get_risk_management_config()
                signal_config = config.get_signal_detection_config()
                
                self.target_symbols = config.get_target_symbols()
                self.min_trade_portion = trading_config.get('min_trade_portion', 1/8)
                self.max_trade_portion = trading_config.get('max_trade_portion', 1/3)
                self.macd_fast = indicators_config.get('macd', {}).get('fast_period', 12)
                self.daily_max_trades = risk_config.get('daily_max_trades', 20)
                self.buy_signals = signal_config.get('buy_signals', {})
                self.sell_signals = signal_config.get('sell_signals', {})
        
        print("=== 交易配置测试 ===")
        mock_config = MockT0TradingConfig()
        
        print(f"交易标的: {mock_config.target_symbols}")
        print(f"最小交易比例: {mock_config.min_trade_portion}")
        print(f"最大交易比例: {mock_config.max_trade_portion}")
        print(f"MACD快线周期: {mock_config.macd_fast}")
        print(f"每日最大交易次数: {mock_config.daily_max_trades}")
        print(f"买入信号配置: {mock_config.buy_signals}")
        print("交易配置测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"交易配置测试失败: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    try:
        from collections import deque
        
        print("=== 数据结构测试 ===")
        
        # 测试分钟数据存储
        minute_data = deque(maxlen=1000)
        
        # 添加测试数据
        for i in range(10):
            minute_bar = {
                'timestamp': datetime.now() - timedelta(minutes=10-i),
                'open': 10.0 + i * 0.1,
                'high': 10.0 + i * 0.1 + 0.05,
                'low': 10.0 + i * 0.1 - 0.05,
                'close': 10.0 + i * 0.1 + 0.02,
                'volume': 1000 + i * 100
            }
            minute_data.append(minute_bar)
        
        print(f"分钟数据条数: {len(minute_data)}")
        print(f"最新数据: {minute_data[-1]}")
        
        # 转换为DataFrame测试
        df = pd.DataFrame(list(minute_data))
        print(f"DataFrame形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        print("数据结构测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"数据结构测试失败: {e}")
        return False

def test_signal_logic():
    """测试信号逻辑"""
    try:
        print("=== 信号逻辑测试 ===")
        
        # 模拟市场数据
        latest_data = {
            'close': 10.50,
            'high': 10.55,
            'low': 10.45,
            'macd_dif': 0.02,
            'kdj_k': 25.0
        }
        
        prev_data = {
            'close': 10.48,
            'macd_dif': 0.01,
            'kdj_k': 22.0
        }
        
        # 测试相对位置计算
        daily_high = 11.00
        daily_low = 10.00
        relative_position = (latest_data['close'] - daily_low) / (daily_high - daily_low)
        
        print(f"当前价格: {latest_data['close']}")
        print(f"日内高点: {daily_high}")
        print(f"日内低点: {daily_low}")
        print(f"相对位置: {relative_position:.2f}")
        
        # 测试买入信号条件
        macd_bullish = latest_data['macd_dif'] > prev_data['macd_dif']
        kdj_oversold = latest_data['kdj_k'] < 30
        kdj_rising = latest_data['kdj_k'] > prev_data['kdj_k']
        
        print(f"MACD上升: {macd_bullish}")
        print(f"KDJ超卖: {kdj_oversold}")
        print(f"KDJ上升: {kdj_rising}")
        
        # 简单的买入信号判断
        buy_signal = (relative_position < 0.6 and 
                     macd_bullish and 
                     kdj_oversold and 
                     kdj_rising)
        
        print(f"买入信号: {buy_signal}")
        print("信号逻辑测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"信号逻辑测试失败: {e}")
        return False



def test_warmup_mechanism():
    """测试warm-up机制"""
    try:
        print("=== Warm-up机制测试 ===")

        from start import MarketDataManager, T0TradingConfig

        config = T0TradingConfig()
        data_manager = MarketDataManager(config)

        # 测试warm-up状态
        test_symbol = "600036.SH"

        # 初始状态应该是未warm-up
        is_ready_before = data_manager.is_symbol_ready_for_trading(test_symbol)
        print(f"初始warm-up状态: {is_ready_before}")

        # 模拟添加足够的数据
        from collections import deque
        from datetime import datetime, timedelta

        data_manager.minute_data[test_symbol] = deque(maxlen=1000)
        data_manager.is_warmed_up[test_symbol] = False

        # 添加足够的历史数据
        base_time = datetime.now() - timedelta(hours=2)
        for i in range(60):  # 添加60分钟数据
            minute_bar = {
                'timestamp': base_time + timedelta(minutes=i),
                'open': 10.0 + i * 0.01,
                'high': 10.1 + i * 0.01,
                'low': 9.9 + i * 0.01,
                'close': 10.05 + i * 0.01,
                'volume': 1000 + i * 10
            }
            data_manager.minute_data[test_symbol].append(minute_bar)

        # 检查是否达到warm-up要求
        if len(data_manager.minute_data[test_symbol]) >= data_manager.warmup_required_periods:
            data_manager.is_warmed_up[test_symbol] = True

        is_ready_after = data_manager.is_symbol_ready_for_trading(test_symbol)
        data_count = len(data_manager.minute_data[test_symbol])

        print(f"添加数据后warm-up状态: {is_ready_after}")
        print(f"数据条数: {data_count}")
        print(f"要求条数: {data_manager.warmup_required_periods}")
        print("Warm-up机制测试通过 ✓")

        return is_ready_after and data_count >= data_manager.warmup_required_periods

    except Exception as e:
        print(f"Warm-up机制测试失败: {e}")
        return False

def test_position_management():
    """测试仓位管理"""
    try:
        print("=== 仓位管理测试 ===")

        from start import PositionManager, T0TradingConfig

        config = T0TradingConfig()
        position_manager = PositionManager(config)

        test_symbol = "600036.SH"

        # 测试基础仓位
        base_position = position_manager.get_current_position(test_symbol)
        print(f"基础仓位: {base_position:.3f}")

        # 测试仓位更新
        new_position = 0.6
        reason = "测试仓位调整"
        position_manager.update_position(test_symbol, new_position, reason)

        updated_position = position_manager.get_current_position(test_symbol)
        print(f"更新后仓位: {updated_position:.3f}")

        # 检查仓位历史
        history_count = len(position_manager.position_history)
        print(f"仓位历史记录数: {history_count}")
        print("仓位管理测试通过 ✓")

        return updated_position == new_position and history_count > 0

    except Exception as e:
        print(f"仓位管理测试失败: {e}")
        return False

def test_trading_time_check():
    """测试交易时间检查"""
    try:
        print("=== 交易时间检查测试 ===")

        from start import MarketDataManager, T0TradingConfig

        config = T0TradingConfig()
        data_manager = MarketDataManager(config)

        # 测试交易时间检查功能
        is_trading_time = data_manager._is_trading_time()
        print(f"当前是否为交易时间: {is_trading_time}")

        # 获取交易时间配置
        from config_loader import config as global_config
        trading_hours = global_config.get_trading_hours()
        print(f"交易时间配置: {trading_hours}")
        print("交易时间检查测试通过 ✓")

        return True  # 功能存在即为通过

    except Exception as e:
        print(f"交易时间检查测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始运行T0交易系统测试...")
    print("=" * 50)

    tests = [
        test_config_loader,
        test_technical_indicators,
        test_trading_config,
        test_data_structures,
        test_signal_logic,
        test_warmup_mechanism,
        test_position_management,
        test_trading_time_check
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"测试 {test.__name__} 发生异常: {e}")
            print()

    print("=" * 50)
    print(f"测试完成: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
