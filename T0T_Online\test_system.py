"""
T0T_Online 系统测试脚本
用于验证系统各个组件的功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_config_loader():
    """测试配置加载器"""
    try:
        from config_loader import config
        
        print("=== 配置加载器测试 ===")
        print(f"交易标的: {config.get_target_symbols()}")
        print(f"MACD参数: {config.get('indicators.macd')}")
        print(f"风险管理: {config.get_risk_management_config()}")
        print(f"实盘交易: {config.is_live_trading_enabled()}")
        print("配置加载器测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"配置加载器测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    try:
        # 导入需要的模块（注意：这里需要模拟smart模块）
        import sys
        sys.path.append('.')
        
        # 创建模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 10.0
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(0.1, new_price))  # 确保价格为正
        
        # 创建DataFrame
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        print("=== 技术指标测试 ===")
        print(f"原始数据形状: {data.shape}")
        print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        # 这里需要实际的TechnicalIndicators类来测试
        # 由于依赖smart模块，我们只能做基本验证
        print("技术指标测试准备完成 ✓")
        return True
        
    except Exception as e:
        print(f"技术指标测试失败: {e}")
        return False

def test_trading_config():
    """测试交易配置"""
    try:
        from config_loader import config
        
        # 模拟T0TradingConfig类的初始化
        class MockT0TradingConfig:
            def __init__(self):
                trading_config = config.get_trading_config()
                indicators_config = config.get_indicators_config()
                risk_config = config.get_risk_management_config()
                signal_config = config.get_signal_detection_config()
                
                self.target_symbols = config.get_target_symbols()
                self.min_trade_portion = trading_config.get('min_trade_portion', 1/8)
                self.max_trade_portion = trading_config.get('max_trade_portion', 1/3)
                self.macd_fast = indicators_config.get('macd', {}).get('fast_period', 12)
                self.daily_max_trades = risk_config.get('daily_max_trades', 20)
                self.buy_signals = signal_config.get('buy_signals', {})
                self.sell_signals = signal_config.get('sell_signals', {})
        
        print("=== 交易配置测试 ===")
        mock_config = MockT0TradingConfig()
        
        print(f"交易标的: {mock_config.target_symbols}")
        print(f"最小交易比例: {mock_config.min_trade_portion}")
        print(f"最大交易比例: {mock_config.max_trade_portion}")
        print(f"MACD快线周期: {mock_config.macd_fast}")
        print(f"每日最大交易次数: {mock_config.daily_max_trades}")
        print(f"买入信号配置: {mock_config.buy_signals}")
        print("交易配置测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"交易配置测试失败: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    try:
        from collections import deque
        
        print("=== 数据结构测试 ===")
        
        # 测试分钟数据存储
        minute_data = deque(maxlen=1000)
        
        # 添加测试数据
        for i in range(10):
            minute_bar = {
                'timestamp': datetime.now() - timedelta(minutes=10-i),
                'open': 10.0 + i * 0.1,
                'high': 10.0 + i * 0.1 + 0.05,
                'low': 10.0 + i * 0.1 - 0.05,
                'close': 10.0 + i * 0.1 + 0.02,
                'volume': 1000 + i * 100
            }
            minute_data.append(minute_bar)
        
        print(f"分钟数据条数: {len(minute_data)}")
        print(f"最新数据: {minute_data[-1]}")
        
        # 转换为DataFrame测试
        df = pd.DataFrame(list(minute_data))
        print(f"DataFrame形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        print("数据结构测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"数据结构测试失败: {e}")
        return False

def test_signal_logic():
    """测试信号逻辑"""
    try:
        print("=== 信号逻辑测试 ===")
        
        # 模拟市场数据
        latest_data = {
            'close': 10.50,
            'high': 10.55,
            'low': 10.45,
            'macd_dif': 0.02,
            'kdj_k': 25.0
        }
        
        prev_data = {
            'close': 10.48,
            'macd_dif': 0.01,
            'kdj_k': 22.0
        }
        
        # 测试相对位置计算
        daily_high = 11.00
        daily_low = 10.00
        relative_position = (latest_data['close'] - daily_low) / (daily_high - daily_low)
        
        print(f"当前价格: {latest_data['close']}")
        print(f"日内高点: {daily_high}")
        print(f"日内低点: {daily_low}")
        print(f"相对位置: {relative_position:.2f}")
        
        # 测试买入信号条件
        macd_bullish = latest_data['macd_dif'] > prev_data['macd_dif']
        kdj_oversold = latest_data['kdj_k'] < 30
        kdj_rising = latest_data['kdj_k'] > prev_data['kdj_k']
        
        print(f"MACD上升: {macd_bullish}")
        print(f"KDJ超卖: {kdj_oversold}")
        print(f"KDJ上升: {kdj_rising}")
        
        # 简单的买入信号判断
        buy_signal = (relative_position < 0.6 and 
                     macd_bullish and 
                     kdj_oversold and 
                     kdj_rising)
        
        print(f"买入信号: {buy_signal}")
        print("信号逻辑测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"信号逻辑测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行T0T_Online系统测试...")
    print("=" * 50)
    
    tests = [
        ("配置加载器", test_config_loader),
        ("技术指标", test_technical_indicators),
        ("交易配置", test_trading_config),
        ("数据结构", test_data_structures),
        ("信号逻辑", test_signal_logic)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
            print()
    
    # 汇总结果
    print("=" * 50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统基础功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关组件")

if __name__ == "__main__":
    run_all_tests()
