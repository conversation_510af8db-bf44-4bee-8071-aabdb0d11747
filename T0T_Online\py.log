2025-06-30 22:35:12,907 - DEBUG - 27908 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - <module> - 23 - Smart API 导入成功
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - __init__ - 59 - 简化版T0系统初始化完成
2025-06-30 22:35:12,976 - INFO - 27908 - start_simple.py - start - 160 - 🚀 启动简化版T0交易系统...
2025-06-30 22:35:12,976 - WARNING - 27908 - start_simple.py - start - 166 - 启动通知发送失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 64 - 开始测试Smart API...
2025-06-30 22:35:12,977 - ERROR - 27908 - start_simple.py - test_smart_api - 71 - ❌ 通知功能测试失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 80 - 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 22:35:12,977 - INFO - 27908 - start_simple.py - test_smart_api - 85 - 测试查询 600036.SH 的数据...
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 99 - ❌ query_data 调用失败: module 'smart' has no attribute 'query_data'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 109 - ❌ query_market_data 也失败: module 'smart' has no attribute 'query_market_data'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 120 - ❌ 订阅功能测试失败: module 'smart' has no attribute 'subscribe'
2025-06-30 22:35:12,978 - ERROR - 27908 - start_simple.py - test_smart_api - 130 - ❌ 定时器设置失败: module 'smart' has no attribute 'add_time_interval'
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 146 - 当前时间: 22:35
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 147 - 是否为交易时间: False
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - test_trading_time - 152 - ⏰ 当前为非交易时间
2025-06-30 22:35:12,978 - WARNING - 27908 - start_simple.py - start - 178 - 成功通知发送失败: module 'smart' has no attribute 'notice'
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - start - 180 - ✅ 简化版T0交易系统启动成功
2025-06-30 22:35:12,978 - INFO - 27908 - start_simple.py - main - 202 - 系统运行中，按 Ctrl+C 停止...
2025-06-30 22:35:32,437 - INFO - 27908 - start_simple.py - main - 209 - 收到停止信号
2025-06-30 22:35:32,438 - INFO - 27908 - start_simple.py - stop - 192 - 简化版T0交易系统已停止
2025-06-30 22:35:58,130 - DEBUG - 36188 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:35:58,154 - INFO - 36188 - check_smart_api.py - <module> - 14 - Smart API 导入成功
2025-06-30 22:36:32,001 - DEBUG - 39176 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:36:32,027 - INFO - 39176 - check_smart_instance.py - <module> - 14 - Smart API 导入成功
2025-06-30 22:37:58,503 - DEBUG - 29416 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:37:59,585 - INFO - 29416 - config_loader.py - load_config - 31 - 成功加载配置文件: config.yaml
2025-06-30 22:38:49,538 - DEBUG - 39160 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:38:49,560 - INFO - 39160 - test_fixed_api.py - <module> - 21 - Smart API 导入成功
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 52 - 🧪 开始测试修复后的Smart API...
2025-06-30 22:38:49,561 - ERROR - 39160 - test_fixed_api.py - test_fixed_smart_api - 59 - ❌ 通知功能测试失败: 'str' object has no attribute 'get'
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 67 - ✅ 前两个交易日: 2025-06-26 到 2025-06-27
2025-06-30 22:38:49,561 - INFO - 39160 - test_fixed_api.py - test_fixed_smart_api - 78 - 测试查询 600036.SH 的数据...
2025-06-30 22:38:49,562 - DEBUG - 39160 - core.py - query_data_async - 2578 - query_data_async start
2025-06-30 22:38:49,562 - DEBUG - 39160 - core.py - query_data_async - 2605 - query_data_async send
2025-06-30 22:40:41,580 - DEBUG - 37428 - logging_config.py - <module> - 75 - initial logging: py.log
2025-06-30 22:40:41,602 - INFO - 37428 - start_fixed.py - <module> - 25 - Smart API 导入成功
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - __init__ - 210 - 修复版T0交易系统初始化完成
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - start - 224 - 🚀 启动修复版T0交易系统...
2025-06-30 22:40:41,603 - WARNING - 37428 - start_fixed.py - _send_notice_safe - 219 - 通知发送失败（不影响系统运行）: 'str' object has no attribute 'get'
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - start - 230 - 开始加载warm-up数据...
2025-06-30 22:40:41,603 - INFO - 37428 - start_fixed.py - load_warmup_data - 130 - 开始加载 600036.SH 的warm-up数据...
2025-06-30 22:40:41,604 - INFO - 37428 - start_fixed.py - load_warmup_data - 136 - 获取 600036.SH 从 2025-06-26 到 2025-06-27 的数据
2025-06-30 22:40:41,604 - DEBUG - 37428 - core.py - query_data_async - 2578 - query_data_async start
2025-06-30 22:40:41,604 - DEBUG - 37428 - core.py - query_data_async - 2605 - query_data_async send
